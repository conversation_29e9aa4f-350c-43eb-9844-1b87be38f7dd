"""
项目管理模块
支持项目的创建、读取、更新、删除，以及本地JSON文件存储
"""

import os
import json
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path

class ProjectManager:
    def __init__(self, projects_folder: str):
        self.projects_folder = Path(projects_folder)
        self.projects_folder.mkdir(exist_ok=True)
        self.projects_index_file = self.projects_folder / "projects_index.json"
        self.projects_cache = {}
        self._load_projects_index()
    
    def _load_projects_index(self):
        """加载项目索引"""
        if self.projects_index_file.exists():
            try:
                with open(self.projects_index_file, 'r', encoding='utf-8') as f:
                    self.projects_cache = json.load(f)
            except (json.JSONDecodeError, IOError) as e:
                print(f"加载项目索引失败: {e}")
                self.projects_cache = {}
        else:
            self.projects_cache = {}
    
    def _save_projects_index(self):
        """保存项目索引"""
        try:
            with open(self.projects_index_file, 'w', encoding='utf-8') as f:
                json.dump(self.projects_cache, f, ensure_ascii=False, indent=2)
        except IOError as e:
            print(f"保存项目索引失败: {e}")
    
    def _get_project_file_path(self, project_id: str) -> Path:
        """获取项目文件路径"""
        return self.projects_folder / f"{project_id}.json"
    
    def create_project(self, name: str, description: str = "", 
                      airport_coords: Optional[Dict] = None) -> str:
        """创建新项目"""
        project_id = str(uuid.uuid4())
        
        # 创建项目数据结构
        project_data = {
            'id': project_id,
            'name': name,
            'description': description,
            'airport_coords': airport_coords or {'lat': 30.314, 'lng': 104.445},
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat(),
            'images': [],
            'regions': [],
            'walkable_regions': [],
            'grid': None,
            'settings': {
                'map_zoom': 16,
                'map_center': airport_coords or {'lat': 30.314, 'lng': 104.445},
                'grid_size': 50,
                'mesh_area': 0.00001
            }
        }
        
        # 保存项目文件
        project_file = self._get_project_file_path(project_id)
        try:
            with open(project_file, 'w', encoding='utf-8') as f:
                json.dump(project_data, f, ensure_ascii=False, indent=2)
        except IOError as e:
            print(f"保存项目文件失败: {e}")
            return None
        
        # 更新项目索引
        self.projects_cache[project_id] = {
            'id': project_id,
            'name': name,
            'description': description,
            'created_at': project_data['created_at'],
            'updated_at': project_data['updated_at'],
            'airport_coords': project_data['airport_coords']
        }
        self._save_projects_index()
        
        return project_id
    
    def get_project(self, project_id: str) -> Optional[Dict]:
        """获取项目详情"""
        print(f"获取项目: {project_id}")
        project_file = self._get_project_file_path(project_id)
        print(f"项目文件路径: {project_file}")
        print(f"文件是否存在: {project_file.exists()}")

        if not project_file.exists():
            print(f"项目文件不存在: {project_file}")
            return None

        try:
            with open(project_file, 'r', encoding='utf-8') as f:
                project_data = json.load(f)
                print(f"成功读取项目文件，项目名称: {project_data.get('name', 'Unknown')}")
                return project_data
        except (json.JSONDecodeError, IOError) as e:
            print(f"读取项目文件失败: {e}")
            return None
    
    def get_all_projects(self) -> List[Dict]:
        """获取所有项目的基本信息"""
        return list(self.projects_cache.values())
    
    def update_project(self, project_id: str, update_data: Dict) -> bool:
        """更新项目"""
        project = self.get_project(project_id)
        if not project:
            return False
        
        # 更新项目数据
        for key, value in update_data.items():
            if key in ['name', 'description', 'airport_coords', 'settings']:
                project[key] = value
        
        project['updated_at'] = datetime.now().isoformat()
        
        # 保存项目文件
        project_file = self._get_project_file_path(project_id)
        try:
            with open(project_file, 'w', encoding='utf-8') as f:
                json.dump(project, f, ensure_ascii=False, indent=2)
        except IOError as e:
            print(f"保存项目文件失败: {e}")
            return False
        
        # 更新索引
        if project_id in self.projects_cache:
            self.projects_cache[project_id].update({
                'name': project.get('name'),
                'description': project.get('description'),
                'updated_at': project['updated_at'],
                'airport_coords': project.get('airport_coords')
            })
            self._save_projects_index()
        
        return True
    
    def delete_project(self, project_id: str) -> bool:
        """删除项目"""
        project_file = self._get_project_file_path(project_id)
        
        # 删除项目文件
        if project_file.exists():
            try:
                project_file.unlink()
            except OSError as e:
                print(f"删除项目文件失败: {e}")
                return False
        
        # 从索引中移除
        if project_id in self.projects_cache:
            del self.projects_cache[project_id]
            self._save_projects_index()
        
        return True
    
    def add_image_to_project(self, project_id: str, image_data: Dict) -> bool:
        """向项目添加图片"""
        project = self.get_project(project_id)
        if not project:
            return False
        
        # 添加图片到项目
        project['images'].append(image_data)
        project['updated_at'] = datetime.now().isoformat()
        
        # 保存项目
        return self._save_project(project_id, project)
    
    def update_image_in_project(self, project_id: str, image_id: str,
                               image_data: Dict) -> bool:
        """更新项目中的图片"""
        print(f"更新项目中的图片: project_id={project_id}, image_id={image_id}")
        print(f"图片数据: {image_data}")

        project = self.get_project(project_id)
        if not project:
            print(f"项目未找到: {project_id}")
            return False

        print(f"项目中的图片数量: {len(project.get('images', []))}")

        # 查找并更新图片属性
        for i, img in enumerate(project['images']):
            print(f"检查图片 {i}: id={img.get('id')}")
            if img['id'] == image_id:
                print(f"找到匹配的图片，准备更新属性")
                # 只更新属性，保留其他信息
                if 'properties' in image_data:
                    if 'properties' not in project['images'][i]:
                        project['images'][i]['properties'] = {}
                    project['images'][i]['properties'].update(image_data['properties'])
                    print(f"属性更新完成: {project['images'][i]['properties']}")

                project['updated_at'] = datetime.now().isoformat()
                result = self._save_project(project_id, project)
                print(f"项目保存结果: {result}")
                return result

        print(f"未找到匹配的图片ID: {image_id}")
        return False
    
    def remove_image_from_project(self, project_id: str, image_id: str) -> bool:
        """从项目中移除图片"""
        project = self.get_project(project_id)
        if not project:
            return False
        
        # 查找并移除图片
        project['images'] = [img for img in project['images'] if img['id'] != image_id]
        project['updated_at'] = datetime.now().isoformat()
        
        return self._save_project(project_id, project)
    
    def add_region_to_project(self, project_id: str, region_data: Dict) -> bool:
        """向项目添加区域"""
        project = self.get_project(project_id)
        if not project:
            return False
        
        # 添加区域到项目
        project['regions'].append(region_data)
        
        # 如果是可寻路区域，也添加到walkable_regions
        if region_data.get('type') in ['walkable', 'airport']:
            project['walkable_regions'].append(region_data)
        
        project['updated_at'] = datetime.now().isoformat()
        
        return self._save_project(project_id, project)

    def clear_regions_in_project(self, project_id: str) -> bool:
        """清除项目中的所有区域"""
        project = self.get_project(project_id)
        if not project:
            return False

        # 清空区域列表
        project['regions'] = []
        project['walkable_regions'] = []
        project['updated_at'] = datetime.now().isoformat()

        return self._save_project(project_id, project)

    def update_region_in_project(self, project_id: str, region_id: str,
                                region_data: Dict) -> bool:
        """更新项目中的区域"""
        project = self.get_project(project_id)
        if not project:
            return False
        
        # 更新regions中的区域
        for i, region in enumerate(project['regions']):
            if region['id'] == region_id:
                project['regions'][i] = region_data
                break
        
        # 更新walkable_regions中的区域
        for i, region in enumerate(project['walkable_regions']):
            if region['id'] == region_id:
                project['walkable_regions'][i] = region_data
                break
        
        project['updated_at'] = datetime.now().isoformat()
        
        return self._save_project(project_id, project)
    
    def set_grid_for_project(self, project_id: str, grid_data: Dict) -> bool:
        """设置项目的网格数据"""
        project = self.get_project(project_id)
        if not project:
            return False
        
        project['grid'] = grid_data
        project['updated_at'] = datetime.now().isoformat()
        
        return self._save_project(project_id, project)
    
    def _save_project(self, project_id: str, project_data: Dict) -> bool:
        """保存项目数据（原子性写入）"""
        project_file = self._get_project_file_path(project_id)
        temp_file = project_file.with_suffix('.tmp')

        try:
            # 先写入临时文件
            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(project_data, f, ensure_ascii=False, indent=2)

            # 验证JSON是否有效
            with open(temp_file, 'r', encoding='utf-8') as f:
                json.load(f)  # 验证JSON格式

            # 原子性替换
            if project_file.exists():
                backup_file = project_file.with_suffix('.bak')
                project_file.rename(backup_file)  # 备份原文件

            temp_file.rename(project_file)  # 原子性替换

            # 删除备份文件
            backup_file = project_file.with_suffix('.bak')
            if backup_file.exists():
                backup_file.unlink()

            print(f"项目文件保存成功: {project_file}")
            return True

        except Exception as e:
            print(f"保存项目文件失败: {e}")
            # 清理临时文件
            if temp_file.exists():
                temp_file.unlink()
            return False
    
    def export_to_kml(self, project: Dict) -> str:
        """导出项目为KML格式"""
        # 简化的KML导出实现
        kml_content = f"""<?xml version="1.0" encoding="UTF-8"?>
<kml xmlns="http://www.opengis.net/kml/2.2">
  <Document>
    <name>{project['name']}</name>
    <description>{project['description']}</description>
    <!-- 这里可以添加更多的KML内容 -->
  </Document>
</kml>"""
        
        # 保存临时KML文件
        kml_file = self.projects_folder / f"{project['id']}.kml"
        with open(kml_file, 'w', encoding='utf-8') as f:
            f.write(kml_content)
        
        return str(kml_file)
