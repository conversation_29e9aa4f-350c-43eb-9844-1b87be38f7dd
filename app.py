"""
机场导航规划平台 - 主应用入口
支持项目管理、图片处理、区域描边、布尔运算、网格拓扑、路径规划
"""

from flask import Flask, render_template, request, jsonify, send_file
from flask_cors import CORS
import os
import json
from datetime import datetime
import uuid

from modules.project_manager import ProjectManager
from modules.image_processor import ImageProcessor
from modules.region_editor import RegionEditor
from modules.boolean_operations import BooleanOperations
from modules.grid_generator import GridGenerator
from modules.path_finder import PathFinder
from modules.coordinate_system import CoordinateSystem

app = Flask(__name__)
CORS(app)

# 配置
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['PROJECTS_FOLDER'] = 'projects'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# 确保目录存在
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs(app.config['PROJECTS_FOLDER'], exist_ok=True)

# 初始化模块
project_manager = ProjectManager(app.config['PROJECTS_FOLDER'])
image_processor = ImageProcessor(app.config['UPLOAD_FOLDER'])
region_editor = RegionEditor()
boolean_ops = BooleanOperations()
grid_generator = GridGenerator()
path_finder = PathFinder()
coord_system = CoordinateSystem()

@app.route('/')
def index():
    """主页面 - 项目列表"""
    return render_template('project_list.html')

@app.route('/project/<project_id>')
def project_editor(project_id):
    """项目编辑页面"""
    return render_template('project_editor.html')

@app.route('/legacy')
def legacy_index():
    """原始的导航演示页面"""
    return render_template('index.html')

@app.route('/api/projects', methods=['GET'])
def get_projects():
    """获取项目列表"""
    projects = project_manager.get_all_projects()
    return jsonify(projects)

@app.route('/api/projects', methods=['POST'])
def create_project():
    """创建新项目"""
    data = request.get_json()
    project_id = project_manager.create_project(
        name=data.get('name'),
        description=data.get('description', ''),
        airport_coords=data.get('airport_coords')
    )
    return jsonify({'project_id': project_id})

@app.route('/api/projects/<project_id>', methods=['GET'])
def get_project(project_id):
    """获取项目详情"""
    print(f"获取项目详情请求: project_id={project_id}")
    project = project_manager.get_project(project_id)
    print(f"项目查询结果: {project is not None}")
    if project:
        print(f"项目数据: {project.get('name', 'Unknown')}, 图片数量: {len(project.get('images', []))}")
    if not project:
        print(f"项目未找到: {project_id}")
        return jsonify({'error': 'Project not found'}), 404
    return jsonify(project)

@app.route('/api/projects/<project_id>', methods=['PUT'])
def update_project(project_id):
    """更新项目"""
    data = request.get_json()
    success = project_manager.update_project(project_id, data)
    if not success:
        return jsonify({'error': 'Project not found'}), 404
    return jsonify({'success': True})

@app.route('/api/projects/<project_id>', methods=['DELETE'])
def delete_project(project_id):
    """删除项目"""
    success = project_manager.delete_project(project_id)
    if not success:
        return jsonify({'error': 'Project not found'}), 404
    return jsonify({'success': True})

@app.route('/api/projects/<project_id>/images', methods=['POST'])
def upload_image(project_id):
    """上传图片到项目"""
    try:
        if 'image' not in request.files:
            return jsonify({'error': 'No image file'}), 400

        file = request.files['image']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400

        # 检查项目是否存在
        project = project_manager.get_project(project_id)
        if not project:
            return jsonify({'error': 'Project not found'}), 404

        # 处理图片
        image_data = image_processor.process_upload(file, project_id)

        # 保存到项目
        success = project_manager.add_image_to_project(project_id, image_data)
        if not success:
            return jsonify({'error': 'Failed to add image to project'}), 500

        return jsonify(image_data)

    except ValueError as e:
        # 用户输入错误（文件格式、大小等）
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        # 服务器内部错误
        print(f"图片上传错误: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/api/projects/<project_id>/images/<image_id>', methods=['PUT'])
def update_image(project_id, image_id):
    """更新图片属性（透明度、缩放、旋转）"""
    data = request.get_json()
    print(f"收到图片属性更新请求: project_id={project_id}, image_id={image_id}")
    print(f"更新数据: {data}")

    # 直接更新项目中的图片属性，不通过image_processor
    success = project_manager.update_image_in_project(project_id, image_id, data)
    print(f"项目中图片信息更新结果: {success}")

    if not success:
        print(f"图片更新失败: {image_id}")
        return jsonify({'error': 'Image update failed'}), 404

    # 获取更新后的项目数据以返回完整的图片信息
    project = project_manager.get_project(project_id)
    if project:
        updated_image = next((img for img in project['images'] if img['id'] == image_id), None)
        if updated_image:
            print(f"返回更新后的完整图片数据: {updated_image}")
            return jsonify(updated_image)

    return jsonify({'error': 'Image not found after update'}), 404

@app.route('/api/projects/<project_id>/regions', methods=['POST'])
def create_region(project_id):
    """创建区域或批量保存区域"""
    data = request.get_json()

    # 检查是否是批量保存
    if 'regions' in data:
        # 批量保存区域
        regions = data.get('regions', [])
        saved_regions = []

        # 清除项目中现有的区域
        project_manager.clear_regions_in_project(project_id)

        for region_data in regions:
            try:
                # 创建区域数据
                region = {
                    'id': region_data.get('id'),
                    'name': region_data.get('name'),
                    'type': region_data.get('type', 'walkable'),
                    'polygon': region_data.get('polygon'),
                    'color': region_data.get('color'),
                    'fillColor': region_data.get('fillColor'),
                    'fillOpacity': region_data.get('fillOpacity', 0.3)
                }

                # 保存到项目
                project_manager.add_region_to_project(project_id, region)
                saved_regions.append(region)

            except Exception as e:
                print(f"保存区域失败: {e}")
                continue

        return jsonify({'saved_regions': saved_regions, 'count': len(saved_regions)})

    else:
        # 单个区域创建（保持向后兼容）
        region_data = region_editor.create_region(
            polygon=data.get('polygon'),
            region_type=data.get('type', 'airport')
        )

        # 保存到项目
        project_manager.add_region_to_project(project_id, region_data)

        return jsonify(region_data)

@app.route('/api/projects/<project_id>/regions/<region_id>', methods=['PUT'])
def update_region(project_id, region_id):
    """更新区域"""
    data = request.get_json()
    
    # 更新区域
    updated_region = region_editor.update_region(region_id, data)
    
    if not updated_region:
        return jsonify({'error': 'Region not found'}), 404
    
    # 更新项目中的区域信息
    project_manager.update_region_in_project(project_id, region_id, updated_region)
    
    return jsonify(updated_region)

@app.route('/api/projects/<project_id>/boolean', methods=['POST'])
def perform_boolean_operation(project_id):
    """执行布尔运算"""
    data = request.get_json()
    
    # 获取项目中的区域
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({'error': 'Project not found'}), 404
    
    # 执行布尔运算
    result = boolean_ops.perform_operation(
        operation=data.get('operation'),
        regions=project.get('regions', []),
        target_region_id=data.get('target_region_id')
    )
    
    # 保存结果到项目
    if result:
        project_manager.add_region_to_project(project_id, result)
    
    return jsonify(result)

@app.route('/api/projects/<project_id>/grid', methods=['POST'])
def generate_grid(project_id):
    """生成网格拓扑"""
    data = request.get_json()

    # 获取项目数据
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({'error': 'Project not found'}), 404

    # 获取区域数据（优先使用请求中的区域数据）
    regions = data.get('regions', [])
    if not regions:
        # 如果请求中没有区域数据，使用项目中保存的区域
        regions = project.get('regions', [])

    # 筛选可行走区域和障碍物区域
    walkable_regions = [r for r in regions if r.get('type') == 'walkable']
    obstacle_regions = [r for r in regions if r.get('type') == 'obstacle']

    if not walkable_regions:
        return jsonify({'error': 'No walkable regions found'}), 400

    # 获取网格参数
    grid_size = data.get('grid_size', 25)  # 默认25米网格
    quality = data.get('quality', 'medium')

    # 根据质量调整网格密度
    quality_multipliers = {
        'low': 2.0,      # 低质量：网格更稀疏
        'medium': 1.0,   # 中等质量：默认密度
        'high': 0.5      # 高质量：网格更密集
    }

    adjusted_grid_size = grid_size * quality_multipliers.get(quality, 1.0)

    try:
        # 生成网格
        grid_data = grid_generator.generate_grid(
            walkable_regions=walkable_regions,
            grid_size=adjusted_grid_size,
            obstacle_regions=obstacle_regions
        )

        if not grid_data:
            return jsonify({'error': 'Failed to generate grid'}), 500

        # 保存网格到项目
        project_manager.set_grid_for_project(project_id, grid_data)

        return jsonify(grid_data)

    except Exception as e:
        print(f"网格生成错误: {e}")
        return jsonify({'error': f'Grid generation failed: {str(e)}'}), 500

@app.route('/api/projects/<project_id>/path', methods=['POST'])
def find_path(project_id):
    """路径规划"""
    data = request.get_json()
    
    # 获取项目数据
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({'error': 'Project not found'}), 404
    
    # 执行路径规划
    path_data = path_finder.find_path(
        start_point=data.get('start'),
        goal_point=data.get('goal'),
        grid_data=project.get('grid'),
        algorithm=data.get('algorithm', 'astar')
    )
    
    return jsonify(path_data)

@app.route('/api/projects/<project_id>/export', methods=['GET'])
def export_project(project_id):
    """导出项目数据"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({'error': 'Project not found'}), 404

    # 导出格式选择
    export_format = request.args.get('format', 'json')

    if export_format == 'kml':
        # 导出为KML格式
        kml_data = project_manager.export_to_kml(project)
        return send_file(kml_data, as_attachment=True,
                        download_name=f'{project["name"]}.kml')
    else:
        # 导出为JSON格式
        return jsonify(project)

# 添加静态文件服务
@app.route('/uploads/<filename>')
def uploaded_file(filename):
    """提供上传的文件"""
    return send_file(os.path.join(app.config['UPLOAD_FOLDER'], filename))

# 原有的导航演示API端点（保持兼容性）
@app.route('/navmesh', methods=['POST'])
def generate_navmesh():
    """生成导航网格（原有功能）"""
    data = request.get_json()
    polygon = data.get('polygon', [])
    max_area = data.get('max_area', 0.00001)

    # 这里可以调用原有的网格生成逻辑
    # 为了简化，返回一个示例响应
    return jsonify({
        'tris': [],
        'message': '请使用新的项目管理API'
    })

@app.route('/path', methods=['POST'])
def find_path_legacy():
    """路径查找（原有功能）"""
    data = request.get_json()

    # 返回示例路径
    return jsonify({
        'path': [],
        'message': '请使用新的项目管理API'
    })

@app.route('/optimize_path', methods=['POST'])
def optimize_path_legacy():
    """路径优化（原有功能）"""
    data = request.get_json()

    # 返回示例路径
    return jsonify({
        'path': [],
        'message': '请使用新的项目管理API'
    })

@app.route('/upload_image', methods=['POST'])
def upload_image_legacy():
    """图片上传（原有功能）"""
    if 'image' not in request.files:
        return jsonify({'error': 'No image file'}), 400

    file = request.files['image']
    if file.filename == '':
        return jsonify({'error': 'No file selected'}), 400

    # 处理图片上传
    image_data = image_processor.process_upload(file, 'legacy')

    return jsonify({
        'url': f"/uploads/{image_data['filename']}",
        'message': '图片上传成功'
    })

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)


