"""
机场导航规划平台 - 主应用入口
支持项目管理、图片处理、区域描边、布尔运算、网格拓扑、路径规划
"""

from flask import Flask, render_template, request, jsonify, send_file
from flask_cors import CORS
import os
import json
from datetime import datetime
import uuid

from modules.project_manager import ProjectManager
from modules.image_processor import ImageProcessor
from modules.region_editor import RegionEditor
from modules.boolean_operations import BooleanOperations
from modules.grid_generator import GridGenerator
from modules.path_finder import PathFinder
from modules.coordinate_system import CoordinateSystem

app = Flask(__name__)
CORS(app)

# 配置
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['PROJECTS_FOLDER'] = 'projects'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# 确保目录存在
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs(app.config['PROJECTS_FOLDER'], exist_ok=True)

# 初始化模块
project_manager = ProjectManager(app.config['PROJECTS_FOLDER'])
image_processor = ImageProcessor(app.config['UPLOAD_FOLDER'])
region_editor = RegionEditor()
boolean_ops = BooleanOperations()
grid_generator = GridGenerator()
path_finder = PathFinder()
coord_system = CoordinateSystem()

@app.route('/')
def index():
    """主页面 - 项目列表"""
    return render_template('project_list.html')

@app.route('/project/<project_id>')
def project_editor(project_id):
    """项目编辑页面"""
    return render_template('project_editor.html')

@app.route('/legacy')
def legacy_index():
    """原始的导航演示页面"""
    return render_template('index.html')

@app.route('/api/projects', methods=['GET'])
def get_projects():
    """获取项目列表"""
    projects = project_manager.get_all_projects()
    return jsonify(projects)

@app.route('/api/projects', methods=['POST'])
def create_project():
    """创建新项目"""
    data = request.get_json()
    project_id = project_manager.create_project(
        name=data.get('name'),
        description=data.get('description', ''),
        airport_coords=data.get('airport_coords')
    )
    return jsonify({'project_id': project_id})

@app.route('/api/projects/<project_id>', methods=['GET'])
def get_project(project_id):
    """获取项目详情"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({'error': 'Project not found'}), 404
    return jsonify(project)

@app.route('/api/projects/<project_id>', methods=['PUT'])
def update_project(project_id):
    """更新项目"""
    data = request.get_json()
    success = project_manager.update_project(project_id, data)
    if not success:
        return jsonify({'error': 'Project not found'}), 404
    return jsonify({'success': True})

@app.route('/api/projects/<project_id>', methods=['DELETE'])
def delete_project(project_id):
    """删除项目"""
    success = project_manager.delete_project(project_id)
    if not success:
        return jsonify({'error': 'Project not found'}), 404
    return jsonify({'success': True})

@app.route('/api/projects/<project_id>/images', methods=['POST'])
def upload_image(project_id):
    """上传图片到项目"""
    try:
        if 'image' not in request.files:
            return jsonify({'error': 'No image file'}), 400

        file = request.files['image']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400

        # 检查项目是否存在
        project = project_manager.get_project(project_id)
        if not project:
            return jsonify({'error': 'Project not found'}), 404

        # 处理图片
        image_data = image_processor.process_upload(file, project_id)

        # 保存到项目
        success = project_manager.add_image_to_project(project_id, image_data)
        if not success:
            return jsonify({'error': 'Failed to add image to project'}), 500

        return jsonify(image_data)

    except ValueError as e:
        # 用户输入错误（文件格式、大小等）
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        # 服务器内部错误
        print(f"图片上传错误: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/api/projects/<project_id>/images/<image_id>', methods=['PUT'])
def update_image(project_id, image_id):
    """更新图片属性（透明度、缩放、旋转）"""
    data = request.get_json()
    
    # 更新图片属性
    updated_image = image_processor.update_image_properties(
        project_id, image_id, data
    )
    
    if not updated_image:
        return jsonify({'error': 'Image not found'}), 404
    
    # 更新项目中的图片信息
    project_manager.update_image_in_project(project_id, image_id, updated_image)
    
    return jsonify(updated_image)

@app.route('/api/projects/<project_id>/regions', methods=['POST'])
def create_region(project_id):
    """创建区域"""
    data = request.get_json()
    region_data = region_editor.create_region(
        polygon=data.get('polygon'),
        region_type=data.get('type', 'airport')
    )
    
    # 保存到项目
    project_manager.add_region_to_project(project_id, region_data)
    
    return jsonify(region_data)

@app.route('/api/projects/<project_id>/regions/<region_id>', methods=['PUT'])
def update_region(project_id, region_id):
    """更新区域"""
    data = request.get_json()
    
    # 更新区域
    updated_region = region_editor.update_region(region_id, data)
    
    if not updated_region:
        return jsonify({'error': 'Region not found'}), 404
    
    # 更新项目中的区域信息
    project_manager.update_region_in_project(project_id, region_id, updated_region)
    
    return jsonify(updated_region)

@app.route('/api/projects/<project_id>/boolean', methods=['POST'])
def perform_boolean_operation(project_id):
    """执行布尔运算"""
    data = request.get_json()
    
    # 获取项目中的区域
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({'error': 'Project not found'}), 404
    
    # 执行布尔运算
    result = boolean_ops.perform_operation(
        operation=data.get('operation'),
        regions=project.get('regions', []),
        target_region_id=data.get('target_region_id')
    )
    
    # 保存结果到项目
    if result:
        project_manager.add_region_to_project(project_id, result)
    
    return jsonify(result)

@app.route('/api/projects/<project_id>/grid', methods=['POST'])
def generate_grid(project_id):
    """生成网格拓扑"""
    data = request.get_json()
    
    # 获取项目的可寻路区域
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({'error': 'Project not found'}), 404
    
    # 生成网格
    grid_data = grid_generator.generate_grid(
        walkable_regions=project.get('walkable_regions', []),
        grid_size=data.get('grid_size', 50)  # 默认50米网格
    )
    
    # 保存网格到项目
    project_manager.set_grid_for_project(project_id, grid_data)
    
    return jsonify(grid_data)

@app.route('/api/projects/<project_id>/path', methods=['POST'])
def find_path(project_id):
    """路径规划"""
    data = request.get_json()
    
    # 获取项目数据
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({'error': 'Project not found'}), 404
    
    # 执行路径规划
    path_data = path_finder.find_path(
        start_point=data.get('start'),
        goal_point=data.get('goal'),
        grid_data=project.get('grid'),
        algorithm=data.get('algorithm', 'astar')
    )
    
    return jsonify(path_data)

@app.route('/api/projects/<project_id>/export', methods=['GET'])
def export_project(project_id):
    """导出项目数据"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({'error': 'Project not found'}), 404

    # 导出格式选择
    export_format = request.args.get('format', 'json')

    if export_format == 'kml':
        # 导出为KML格式
        kml_data = project_manager.export_to_kml(project)
        return send_file(kml_data, as_attachment=True,
                        download_name=f'{project["name"]}.kml')
    else:
        # 导出为JSON格式
        return jsonify(project)

# 添加静态文件服务
@app.route('/uploads/<filename>')
def uploaded_file(filename):
    """提供上传的文件"""
    return send_file(os.path.join(app.config['UPLOAD_FOLDER'], filename))

# 原有的导航演示API端点（保持兼容性）
@app.route('/navmesh', methods=['POST'])
def generate_navmesh():
    """生成导航网格（原有功能）"""
    data = request.get_json()
    polygon = data.get('polygon', [])
    max_area = data.get('max_area', 0.00001)

    # 这里可以调用原有的网格生成逻辑
    # 为了简化，返回一个示例响应
    return jsonify({
        'tris': [],
        'message': '请使用新的项目管理API'
    })

@app.route('/path', methods=['POST'])
def find_path_legacy():
    """路径查找（原有功能）"""
    data = request.get_json()

    # 返回示例路径
    return jsonify({
        'path': [],
        'message': '请使用新的项目管理API'
    })

@app.route('/optimize_path', methods=['POST'])
def optimize_path_legacy():
    """路径优化（原有功能）"""
    data = request.get_json()

    # 返回示例路径
    return jsonify({
        'path': [],
        'message': '请使用新的项目管理API'
    })

@app.route('/upload_image', methods=['POST'])
def upload_image_legacy():
    """图片上传（原有功能）"""
    if 'image' not in request.files:
        return jsonify({'error': 'No image file'}), 400

    file = request.files['image']
    if file.filename == '':
        return jsonify({'error': 'No file selected'}), 400

    # 处理图片上传
    image_data = image_processor.process_upload(file, 'legacy')

    return jsonify({
        'url': f"/uploads/{image_data['filename']}",
        'message': '图片上传成功'
    })

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)


