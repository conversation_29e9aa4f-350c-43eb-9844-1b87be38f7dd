#!/usr/bin/env python3
"""
测试大图片上传功能
"""

import requests
import os
from PIL import Image, ImageDraw
import io

def create_large_test_image():
    """创建一个大的测试图片"""
    # 创建一个较大的测试图片 (2048x2048)
    img = Image.new('RGB', (2048, 2048), color='lightblue')
    draw = ImageDraw.Draw(img)
    
    # 绘制一些图案
    for i in range(0, 2048, 100):
        draw.line([(i, 0), (i, 2048)], fill='white', width=2)
        draw.line([(0, i), (2048, i)], fill='white', width=2)
    
    # 绘制一些圆形
    for x in range(200, 2048, 400):
        for y in range(200, 2048, 400):
            draw.ellipse([x-50, y-50, x+50, y+50], fill='red', outline='darkred', width=3)
    
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='PNG')
    img_bytes.seek(0)
    return img_bytes

def test_large_upload():
    """测试大图片上传功能"""
    project_id = "e8a31bb8-3584-429f-82b7-feab15ff2583"
    url = f"http://localhost:5000/api/projects/{project_id}/images"
    
    # 创建大测试图片
    print("创建大测试图片...")
    img_data = create_large_test_image()
    
    try:
        files = {
            'image': ('large_test_image.png', img_data, 'image/png')
        }
        
        print(f"上传大图片到: {url}")
        response = requests.post(url, files=files)
        
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print("大图片上传成功!")
            print(f"图片ID: {result.get('id')}")
            print(f"文件名: {result.get('filename')}")
            print(f"尺寸: {result.get('width')} x {result.get('height')}")
            print(f"文件大小: {result.get('file_size')} bytes")
        else:
            print("大图片上传失败!")
            
    except Exception as e:
        print(f"请求异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_large_upload()
