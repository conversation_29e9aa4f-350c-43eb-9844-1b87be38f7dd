"""
坐标系统模块
处理WGS84坐标系转换和验证
"""

import math
from typing import Tuple, Dict, List, Optional

class CoordinateSystem:
    def __init__(self):
        # WGS84椭球参数
        self.a = 6378137.0  # 长半轴（米）
        self.f = 1/298.257223563  # 扁率
        self.b = self.a * (1 - self.f)  # 短半轴
        self.e2 = 2 * self.f - self.f * self.f  # 第一偏心率平方
    
    def validate_coordinates(self, lat: float, lng: float) -> bool:
        """验证坐标是否有效"""
        return (-90 <= lat <= 90) and (-180 <= lng <= 180)
    
    def distance_between_points(self, point1: Tuple[float, float], 
                               point2: Tuple[float, float]) -> float:
        """计算两点间距离（米）"""
        lat1, lng1 = point1
        lat2, lng2 = point2
        
        # 使用Haversine公式
        R = 6371000  # 地球半径（米）
        phi1 = math.radians(lat1)
        phi2 = math.radians(lat2)
        dphi = math.radians(lat2 - lat1)
        dlambda = math.radians(lng2 - lng1)
        
        a = (math.sin(dphi/2)**2 + 
             math.cos(phi1) * math.cos(phi2) * math.sin(dlambda/2)**2)
        
        return 2 * R * math.atan2(math.sqrt(a), math.sqrt(1-a))
    
    def bearing_between_points(self, point1: Tuple[float, float], 
                              point2: Tuple[float, float]) -> float:
        """计算两点间的方位角（度）"""
        lat1, lng1 = point1
        lat2, lng2 = point2
        
        phi1 = math.radians(lat1)
        phi2 = math.radians(lat2)
        dlambda = math.radians(lng2 - lng1)
        
        y = math.sin(dlambda) * math.cos(phi2)
        x = (math.cos(phi1) * math.sin(phi2) - 
             math.sin(phi1) * math.cos(phi2) * math.cos(dlambda))
        
        bearing = math.degrees(math.atan2(y, x))
        return (bearing + 360) % 360  # 转换为0-360度
    
    def point_at_distance_bearing(self, start_point: Tuple[float, float], 
                                 distance: float, bearing: float) -> Tuple[float, float]:
        """从起点按距离和方位角计算终点"""
        lat1, lng1 = start_point
        R = 6371000  # 地球半径（米）
        
        phi1 = math.radians(lat1)
        lambda1 = math.radians(lng1)
        alpha = math.radians(bearing)
        delta = distance / R
        
        phi2 = math.asin(
            math.sin(phi1) * math.cos(delta) + 
            math.cos(phi1) * math.sin(delta) * math.cos(alpha)
        )
        
        lambda2 = lambda1 + math.atan2(
            math.sin(alpha) * math.sin(delta) * math.cos(phi1),
            math.cos(delta) - math.sin(phi1) * math.sin(phi2)
        )
        
        return (math.degrees(phi2), math.degrees(lambda2))
    
    def meters_to_degrees(self, meters: float, latitude: float) -> Tuple[float, float]:
        """将米转换为经纬度度数"""
        # 纬度转换（1度纬度约等于111320米）
        lat_degrees = meters / 111320.0
        
        # 经度转换（取决于纬度）
        lng_degrees = meters / (40075000 * math.cos(math.radians(latitude)) / 360)
        
        return lat_degrees, lng_degrees
    
    def degrees_to_meters(self, lat_degrees: float, lng_degrees: float, 
                         latitude: float) -> Tuple[float, float]:
        """将经纬度度数转换为米"""
        # 纬度转换
        lat_meters = lat_degrees * 111320.0
        
        # 经度转换
        lng_meters = lng_degrees * (40075000 * math.cos(math.radians(latitude)) / 360)
        
        return lat_meters, lng_meters
    
    def calculate_bounds(self, center_point: Tuple[float, float], 
                        width_meters: float, height_meters: float) -> Dict:
        """计算矩形边界"""
        lat, lng = center_point
        
        # 计算半宽和半高
        half_width = width_meters / 2
        half_height = height_meters / 2
        
        # 转换为度数
        lat_offset, lng_offset = self.meters_to_degrees(
            max(half_width, half_height), lat
        )
        
        return {
            'north': lat + lat_offset,
            'south': lat - lat_offset,
            'east': lng + lng_offset,
            'west': lng - lng_offset,
            'center': center_point,
            'width_meters': width_meters,
            'height_meters': height_meters
        }
    
    def is_point_in_bounds(self, point: Tuple[float, float], 
                          bounds: Dict) -> bool:
        """判断点是否在边界内"""
        lat, lng = point
        return (bounds['south'] <= lat <= bounds['north'] and 
                bounds['west'] <= lng <= bounds['east'])
    
    def calculate_polygon_area(self, polygon: List[Tuple[float, float]]) -> float:
        """计算多边形面积（平方米）"""
        if len(polygon) < 3:
            return 0
        
        # 使用球面几何计算面积
        area = 0
        n = len(polygon)
        
        for i in range(n):
            j = (i + 1) % n
            lat1, lng1 = polygon[i]
            lat2, lng2 = polygon[j]
            
            area += math.radians(lng2 - lng1) * (2 + math.sin(math.radians(lat1)) + 
                                                math.sin(math.radians(lat2)))
        
        area = abs(area) * self.a * self.a / 2
        return area
    
    def calculate_polygon_center(self, polygon: List[Tuple[float, float]]) -> Tuple[float, float]:
        """计算多边形中心点"""
        if not polygon:
            return (0, 0)
        
        # 简单平均（对于小区域足够准确）
        total_lat = sum(point[0] for point in polygon)
        total_lng = sum(point[1] for point in polygon)
        
        return (total_lat / len(polygon), total_lng / len(polygon))
    
    def transform_coordinates(self, coordinates: List[Tuple[float, float]], 
                            transform_type: str = "none") -> List[Tuple[float, float]]:
        """坐标变换"""
        if transform_type == "none":
            return coordinates
        
        # 这里可以添加其他坐标系统转换
        # 例如：UTM、Mercator等
        
        return coordinates
    
    def validate_polygon_coordinates(self, polygon: List[Tuple[float, float]]) -> Dict:
        """验证多边形坐标"""
        if len(polygon) < 3:
            return {'valid': False, 'error': '多边形至少需要3个点'}
        
        invalid_points = []
        for i, (lat, lng) in enumerate(polygon):
            if not self.validate_coordinates(lat, lng):
                invalid_points.append(i)
        
        if invalid_points:
            return {
                'valid': False, 
                'error': f'无效的坐标点: {invalid_points}'
            }
        
        # 检查多边形是否闭合
        if polygon[0] != polygon[-1]:
            return {
                'valid': False,
                'error': '多边形未闭合'
            }
        
        # 计算面积
        area = self.calculate_polygon_area(polygon)
        
        return {
            'valid': True,
            'area': area,
            'center': self.calculate_polygon_center(polygon)
        }


