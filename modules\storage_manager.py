"""
存储管理模块
负责所有数据的本地持久化存储，包括项目配置、图片信息、区域数据等
"""

import os
import json
import shutil
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path

class StorageManager:
    def __init__(self, base_folder: str):
        self.base_folder = Path(base_folder)
        self.base_folder.mkdir(exist_ok=True)
        
        # 创建子目录
        self.projects_folder = self.base_folder / "projects"
        self.uploads_folder = self.base_folder / "uploads"
        self.backups_folder = self.base_folder / "backups"
        self.cache_folder = self.base_folder / "cache"
        
        for folder in [self.projects_folder, self.uploads_folder, 
                      self.backups_folder, self.cache_folder]:
            folder.mkdir(exist_ok=True)
        
        # 配置文件
        self.config_file = self.base_folder / "app_config.json"
        self.load_config()
    
    def load_config(self):
        """加载应用配置"""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
            except (json.JSONDecodeError, IOError):
                self.config = self._get_default_config()
        else:
            self.config = self._get_default_config()
            self.save_config()
    
    def save_config(self):
        """保存应用配置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except IOError as e:
            print(f"保存配置失败: {e}")
    
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            'version': '1.0.0',
            'created_at': datetime.now().isoformat(),
            'settings': {
                'auto_backup': True,
                'backup_interval': 24,  # 小时
                'max_backups': 10,
                'image_quality': 0.9,
                'max_image_size': 16 * 1024 * 1024,  # 16MB
                'supported_formats': ['jpg', 'jpeg', 'png', 'bmp', 'tiff']
            },
            'ui_preferences': {
                'theme': 'light',
                'language': 'zh-CN',
                'map_provider': 'arcgis',
                'default_zoom': 16
            }
        }
    
    def create_backup(self, project_id: str = None) -> str:
        """创建备份"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if project_id:
            # 单个项目备份
            backup_name = f"project_{project_id}_{timestamp}.zip"
            backup_path = self.backups_folder / backup_name
            
            # 创建项目备份
            project_folder = self.projects_folder / project_id
            if project_folder.exists():
                shutil.make_archive(
                    str(backup_path.with_suffix('')), 
                    'zip', 
                    str(project_folder)
                )
        else:
            # 全量备份
            backup_name = f"full_backup_{timestamp}.zip"
            backup_path = self.backups_folder / backup_name
            
            # 创建全量备份
            shutil.make_archive(
                str(backup_path.with_suffix('')), 
                'zip', 
                str(self.base_folder),
                ignore=shutil.ignore_patterns('backups', 'cache', '*.tmp')
            )
        
        # 清理旧备份
        self._cleanup_old_backups()
        
        return str(backup_path)
    
    def _cleanup_old_backups(self):
        """清理旧备份"""
        max_backups = self.config['settings']['max_backups']
        backup_files = list(self.backups_folder.glob('*.zip'))
        backup_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        
        # 删除超出数量的备份
        for backup_file in backup_files[max_backups:]:
            try:
                backup_file.unlink()
            except OSError:
                pass
    
    def restore_backup(self, backup_path: str, target_folder: str = None) -> bool:
        """恢复备份"""
        backup_file = Path(backup_path)
        if not backup_file.exists():
            return False
        
        target = Path(target_folder) if target_folder else self.base_folder
        
        try:
            shutil.unpack_archive(str(backup_file), str(target))
            return True
        except Exception as e:
            print(f"恢复备份失败: {e}")
            return False
    
    def get_storage_stats(self) -> Dict:
        """获取存储统计信息"""
        def get_folder_size(folder_path: Path) -> int:
            total_size = 0
            if folder_path.exists():
                for file_path in folder_path.rglob('*'):
                    if file_path.is_file():
                        total_size += file_path.stat().st_size
            return total_size
        
        stats = {
            'total_size': get_folder_size(self.base_folder),
            'projects_size': get_folder_size(self.projects_folder),
            'uploads_size': get_folder_size(self.uploads_folder),
            'backups_size': get_folder_size(self.backups_folder),
            'cache_size': get_folder_size(self.cache_folder),
            'project_count': len(list(self.projects_folder.glob('*.json'))),
            'image_count': len(list(self.uploads_folder.glob('*'))),
            'backup_count': len(list(self.backups_folder.glob('*.zip')))
        }
        
        return stats
    
    def cleanup_orphaned_files(self) -> Dict:
        """清理孤立文件"""
        # 获取所有项目引用的文件
        referenced_files = set()
        
        for project_file in self.projects_folder.glob('*.json'):
            try:
                with open(project_file, 'r', encoding='utf-8') as f:
                    project_data = json.load(f)
                
                # 收集项目中引用的图片文件
                for image in project_data.get('images', []):
                    if 'filename' in image:
                        referenced_files.add(image['filename'])
                        
            except (json.JSONDecodeError, IOError):
                continue
        
        # 查找孤立文件
        orphaned_files = []
        total_size = 0
        
        for file_path in self.uploads_folder.iterdir():
            if file_path.is_file() and file_path.name not in referenced_files:
                orphaned_files.append(str(file_path))
                total_size += file_path.stat().st_size
        
        # 删除孤立文件
        deleted_count = 0
        for file_path in orphaned_files:
            try:
                Path(file_path).unlink()
                deleted_count += 1
            except OSError:
                pass
        
        return {
            'deleted_count': deleted_count,
            'total_files': len(orphaned_files),
            'freed_space': total_size
        }
    
    def export_project_data(self, project_id: str, export_format: str = 'json') -> Optional[str]:
        """导出项目数据"""
        project_file = self.projects_folder / f"{project_id}.json"
        if not project_file.exists():
            return None
        
        try:
            with open(project_file, 'r', encoding='utf-8') as f:
                project_data = json.load(f)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            if export_format.lower() == 'json':
                export_file = self.cache_folder / f"export_{project_id}_{timestamp}.json"
                with open(export_file, 'w', encoding='utf-8') as f:
                    json.dump(project_data, f, ensure_ascii=False, indent=2)
                
            elif export_format.lower() == 'kml':
                export_file = self.cache_folder / f"export_{project_id}_{timestamp}.kml"
                kml_content = self._generate_kml(project_data)
                with open(export_file, 'w', encoding='utf-8') as f:
                    f.write(kml_content)
            
            else:
                return None
            
            return str(export_file)
            
        except (json.JSONDecodeError, IOError) as e:
            print(f"导出项目数据失败: {e}")
            return None
    
    def _generate_kml(self, project_data: Dict) -> str:
        """生成KML格式数据"""
        kml_template = '''<?xml version="1.0" encoding="UTF-8"?>
<kml xmlns="http://www.opengis.net/kml/2.2">
  <Document>
    <name>{name}</name>
    <description>{description}</description>
    {placemarks}
  </Document>
</kml>'''
        
        placemarks = []
        
        # 添加机场位置
        airport_coords = project_data.get('airport_coords', {})
        if airport_coords:
            placemark = f'''
    <Placemark>
      <name>机场位置</name>
      <Point>
        <coordinates>{airport_coords.get('lng', 0)},{airport_coords.get('lat', 0)},0</coordinates>
      </Point>
    </Placemark>'''
            placemarks.append(placemark)
        
        # 添加区域
        for region in project_data.get('regions', []):
            if 'polygon' in region:
                coords = []
                for point in region['polygon']:
                    coords.append(f"{point[1]},{point[0]},0")
                
                placemark = f'''
    <Placemark>
      <name>{region.get('name', '未命名区域')}</name>
      <description>{region.get('description', '')}</description>
      <Polygon>
        <outerBoundaryIs>
          <LinearRing>
            <coordinates>{' '.join(coords)}</coordinates>
          </LinearRing>
        </outerBoundaryIs>
      </Polygon>
    </Placemark>'''
                placemarks.append(placemark)
        
        return kml_template.format(
            name=project_data.get('name', '未命名项目'),
            description=project_data.get('description', ''),
            placemarks=''.join(placemarks)
        )
    
    def import_project_data(self, import_file: str) -> Optional[str]:
        """导入项目数据"""
        import_path = Path(import_file)
        if not import_path.exists():
            return None
        
        try:
            with open(import_path, 'r', encoding='utf-8') as f:
                project_data = json.load(f)
            
            # 生成新的项目ID
            import uuid
            new_project_id = str(uuid.uuid4())
            project_data['id'] = new_project_id
            project_data['imported_at'] = datetime.now().isoformat()
            
            # 保存导入的项目
            project_file = self.projects_folder / f"{new_project_id}.json"
            with open(project_file, 'w', encoding='utf-8') as f:
                json.dump(project_data, f, ensure_ascii=False, indent=2)
            
            return new_project_id
            
        except (json.JSONDecodeError, IOError) as e:
            print(f"导入项目数据失败: {e}")
            return None
    
    def get_recent_projects(self, limit: int = 10) -> List[Dict]:
        """获取最近访问的项目"""
        projects = []
        
        for project_file in self.projects_folder.glob('*.json'):
            try:
                with open(project_file, 'r', encoding='utf-8') as f:
                    project_data = json.load(f)
                
                projects.append({
                    'id': project_data.get('id'),
                    'name': project_data.get('name'),
                    'updated_at': project_data.get('updated_at'),
                    'file_mtime': project_file.stat().st_mtime
                })
                
            except (json.JSONDecodeError, IOError):
                continue
        
        # 按修改时间排序
        projects.sort(key=lambda x: x['file_mtime'], reverse=True)
        
        return projects[:limit]
