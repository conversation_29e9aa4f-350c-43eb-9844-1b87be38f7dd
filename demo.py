from flask import Flask, render_template_string, request, jsonify, send_from_directory
from werkzeug.utils import secure_filename
import os
import numpy as np
import triangle as tr
import shapely.geometry as geom
import math
import heapq

app = Flask(__name__)

# ---------------- 配置与目录 ---------------- #
app.config['UPLOAD_FOLDER'] = os.path.join(os.path.dirname(__file__), 'uploads')
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# ---------------- 工具函数 ---------------- #
def haversine(lon1, lat1, lon2, lat2):
    R = 6371000
    phi1, phi2 = math.radians(lat1), math.radians(lat2)
    dphi = math.radians(lat2-lat1)
    dlambda = math.radians(lon2-lon1)
    a = math.sin(dphi/2)**2 + math.cos(phi1)*math.cos(phi2)*math.sin(dlambda/2)**2
    return 2*R*math.atan2(math.sqrt(a), math.sqrt(1-a))

def line_in_polygon(p1, p2, polygon):
    poly = geom.Polygon([(p[1],p[0]) for p in polygon])
    line = geom.LineString([(p1[1],p1[0]), (p2[1],p2[0])])
    return poly.contains(line)

def astar(nodes, adj, start_idx, goal_idx):
    open_set=[]
    heapq.heappush(open_set,(0,start_idx))
    came_from={}
    gscore={start_idx:0}
    while open_set:
        _, current = heapq.heappop(open_set)
        if current==goal_idx:
            path=[current]
            while current in came_from:
                current=came_from[current]
                path.append(current)
            return path[::-1]
        for neighbor in adj[current]:
            tentative_g=gscore[current]+haversine(*nodes[current],*nodes[neighbor])
            if tentative_g<gscore.get(neighbor,1e9):
                came_from[neighbor]=current
                gscore[neighbor]=tentative_g
                fscore=tentative_g+haversine(*nodes[neighbor],*nodes[goal_idx])
                heapq.heappush(open_set,(fscore,neighbor))
    return []

# ---------------- 路由 ---------------- #
@app.route("/")
def index():
    return render_template_string(open("templates/index.html", encoding="utf-8").read())

# ---------------- 静态文件（上传图片）服务 ---------------- #
@app.route('/uploads/<path:filename>')
def uploaded_file(filename):
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

# ---------------- 图片上传接口 ---------------- #
@app.route('/upload_image', methods=['POST'])
def upload_image():
    if 'image' not in request.files:
        return jsonify({'error': 'no_file'}), 400
    file = request.files['image']
    if not file or file.filename is None:
        return jsonify({'error': 'empty_filename'}), 400
    # 处理可能为空的安全文件名（非 ASCII 名称被清空时）
    raw_filename = file.filename
    filename = secure_filename(raw_filename or '')
    name, ext = os.path.splitext(filename)
    if not ext:
        # 尝试从原始名获取扩展名
        _, ext_raw = os.path.splitext(raw_filename)
        ext = ext_raw or '.png'
    if not name:
        import uuid as _uuid
        name = _uuid.uuid4().hex
    # 防重名
    counter = 1
    save_name = f"{name}{ext}"
    while os.path.exists(os.path.join(app.config['UPLOAD_FOLDER'], save_name)):
        save_name = f"{name}_{counter}{ext}"
        counter += 1
    try:
        file.save(os.path.join(app.config['UPLOAD_FOLDER'], save_name))
    except Exception as e:
        return jsonify({'error': 'save_failed', 'message': str(e)}), 500
    return jsonify({'filename': save_name, 'url': f"/uploads/{save_name}"})

@app.route('/delete_image', methods=['POST'])
def delete_image():
    data = request.get_json(silent=True) or {}
    filename = data.get('filename') or request.args.get('filename')
    if not filename:
        return jsonify({'error': 'no_filename'}), 400
    # 安全处理文件名
    filename = secure_filename(filename)
    path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
    if not os.path.exists(path):
        return jsonify({'ok': True, 'message': 'file_not_found'}), 200
    try:
        os.remove(path)
        return jsonify({'ok': True})
    except OSError:
        return jsonify({'ok': False}), 500

@app.route("/navmesh", methods=["POST"])
def navmesh():
    data = request.get_json()
    poly = data["polygon"]
    max_area = data.get("max_area", 0.00001)

    coords = [(p[1], p[0]) for p in poly]
    poly_dict = {
        "vertices": np.array(coords),
        "segments": np.array([[i, (i+1)%len(coords)] for i in range(len(coords))])
    }
    t = tr.triangulate(poly_dict, f"pq30a{max_area}")

    tris = []
    for tri in t["triangles"]:
        pts = [t["vertices"][i] for i in tri]
        tris.append([[y, x] for x, y in pts])

    return jsonify({"tris": tris})

@app.route("/path", methods=["POST"])
def path():
    data = request.get_json()
    poly = data["polygon"]
    start = data["start"]
    goal = data["goal"]

    coords=[(p[1],p[0]) for p in poly]
    poly_dict={
        "vertices": np.array(coords),
        "segments": np.array([[i,(i+1)%len(coords)] for i in range(len(coords))])
    }

    t=tr.triangulate(poly_dict,"p")
    tris=[[t["vertices"][i] for i in tri] for tri in t["triangles"]]

    nodes=[]
    node_map={}
    for tri in tris:
        for pt in tri:
            key=(pt[1],pt[0])
            if key not in node_map:
                node_map[key]=len(nodes)
                nodes.append([pt[1],pt[0]])

    start_idx=len(nodes)
    nodes.append(start)
    goal_idx=len(nodes)
    nodes.append(goal)

    adj={i:set() for i in range(len(nodes))}
    for tri in tris:
        idxs=[node_map[(pt[1],pt[0])] for pt in tri]
        for i in range(3):
            for j in range(i+1,3):
                if line_in_polygon(nodes[idxs[i]], nodes[idxs[j]], poly):
                    adj[idxs[i]].add(idxs[j])
                    adj[idxs[j]].add(idxs[i])

    for i in range(len(nodes)-2):
        if line_in_polygon(start, nodes[i], poly):
            adj[start_idx].add(i)
            adj[i].add(start_idx)
        if line_in_polygon(goal, nodes[i], poly):
            adj[goal_idx].add(i)
            adj[i].add(goal_idx)
    if line_in_polygon(start, goal, poly):
        adj[start_idx].add(goal_idx)
        adj[goal_idx].add(start_idx)

    idx_path=astar(nodes, adj, start_idx, goal_idx)
    path_coords=[nodes[i] for i in idx_path]
    return jsonify({"path":path_coords})

# ---------------- 新增优化路径 ---------------- #
@app.route("/optimize_path", methods=["POST"])
def optimize_path():
    data = request.get_json()
    poly = data["polygon"]
    start = data["start"]
    goal = data["goal"]

    coords=[(p[1],p[0]) for p in poly]
    poly_dict={
        "vertices": np.array(coords),
        "segments": np.array([[i,(i+1)%len(coords)] for i in range(len(coords))])
    }
    t=tr.triangulate(poly_dict,"p")
    tris=[[t["vertices"][i] for i in tri] for tri in t["triangles"]]

    # 可见图节点
    nodes=[]
    node_map={}
    for tri in tris:
        for pt in tri:
            key=(pt[1],pt[0])
            if key not in node_map:
                node_map[key]=len(nodes)
                nodes.append([pt[1],pt[0]])
    start_idx=len(nodes)
    nodes.append(start)
    goal_idx=len(nodes)
    nodes.append(goal)

    adj={i:set() for i in range(len(nodes))}
    # 构建可见图：两节点连线完全在多边形内部
    for i in range(len(nodes)):
        for j in range(i+1,len(nodes)):
            if line_in_polygon(nodes[i], nodes[j], poly):
                adj[i].add(j)
                adj[j].add(i)

    idx_path=astar(nodes, adj, start_idx, goal_idx)
    path_coords=[nodes[i] for i in idx_path]
    return jsonify({"path":path_coords})

if __name__=="__main__":
    app.run(debug=True, use_reloader=False)
