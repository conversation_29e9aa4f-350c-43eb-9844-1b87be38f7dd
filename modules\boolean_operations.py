"""
布尔运算模块
负责区域的布尔运算（合并、相交、差集等）
"""

import uuid
from typing import Dict, List, Optional
from shapely.geometry import Polygon
from shapely.ops import unary_union

class BooleanOperations:
    def __init__(self):
        pass
    
    def perform_operation(self, operation: str, regions: List[Dict], 
                         target_region_id: Optional[str] = None) -> Optional[Dict]:
        """执行布尔运算"""
        if not regions:
            return None
        
        try:
            if operation == "union":
                return self._union_operation(regions)
            elif operation == "intersection":
                return self._intersection_operation(regions)
            elif operation == "difference":
                return self._difference_operation(regions, target_region_id)
            elif operation == "symmetric_difference":
                return self._symmetric_difference_operation(regions)
            elif operation == "subtract_obstacles":
                return self._subtract_obstacles(regions)
            else:
                raise ValueError(f"不支持的布尔运算: {operation}")
        
        except Exception as e:
            print(f"布尔运算错误: {e}")
            return None
    
    def _union_operation(self, regions: List[Dict]) -> Dict:
        """合并操作 - 将所有区域合并为一个"""
        if not regions:
            return None
        
        # 获取所有多边形
        polygons = []
        for region in regions:
            if 'polygon' in region:
                polygon = Polygon(region['polygon'])
                if polygon.is_valid:
                    polygons.append(polygon)
        
        if not polygons:
            return None
        
        # 执行合并
        merged_polygon = unary_union(polygons)
        
        # 处理结果
        if merged_polygon.geom_type == 'Polygon':
            result_coords = list(merged_polygon.exterior.coords)
        elif merged_polygon.geom_type == 'MultiPolygon':
            # 取最大的多边形
            largest_polygon = max(merged_polygon.geoms, key=lambda p: p.area)
            result_coords = list(largest_polygon.exterior.coords)
        else:
            return None
        
        return {
            'id': str(uuid.uuid4()),
            'name': 'merged_region',
            'description': '合并后的区域',
            'type': 'walkable',
            'polygon': result_coords,
            'area': merged_polygon.area,
            'bounds': merged_polygon.bounds,
            'operation': 'union',
            'source_regions': [r['id'] for r in regions]
        }
    
    def _intersection_operation(self, regions: List[Dict]) -> Dict:
        """相交操作 - 获取所有区域的交集"""
        if len(regions) < 2:
            return None
        
        # 获取第一个多边形作为基础
        base_polygon = Polygon(regions[0]['polygon'])
        if not base_polygon.is_valid:
            return None
        
        # 依次与其他多边形求交集
        result_polygon = base_polygon
        for region in regions[1:]:
            polygon = Polygon(region['polygon'])
            if polygon.is_valid:
                result_polygon = result_polygon.intersection(polygon)
                if result_polygon.is_empty:
                    return None
        
        if result_polygon.geom_type == 'Polygon':
            result_coords = list(result_polygon.exterior.coords)
        else:
            return None
        
        return {
            'id': str(uuid.uuid4()),
            'name': 'intersection_region',
            'description': '相交区域',
            'type': 'walkable',
            'polygon': result_coords,
            'area': result_polygon.area,
            'bounds': result_polygon.bounds,
            'operation': 'intersection',
            'source_regions': [r['id'] for r in regions]
        }
    
    def _difference_operation(self, regions: List[Dict], 
                            target_region_id: Optional[str]) -> Dict:
        """差集操作 - 从目标区域中减去其他区域"""
        if not target_region_id:
            return None
        
        # 找到目标区域
        target_region = None
        other_regions = []
        
        for region in regions:
            if region['id'] == target_region_id:
                target_region = region
            else:
                other_regions.append(region)
        
        if not target_region:
            return None
        
        target_polygon = Polygon(target_region['polygon'])
        if not target_polygon.is_valid:
            return None
        
        # 从目标区域中减去其他区域
        result_polygon = target_polygon
        for region in other_regions:
            polygon = Polygon(region['polygon'])
            if polygon.is_valid:
                result_polygon = result_polygon.difference(polygon)
        
        if result_polygon.is_empty:
            return None
        
        if result_polygon.geom_type == 'Polygon':
            result_coords = list(result_polygon.exterior.coords)
        elif result_polygon.geom_type == 'MultiPolygon':
            # 取最大的多边形
            largest_polygon = max(result_polygon.geoms, key=lambda p: p.area)
            result_coords = list(largest_polygon.exterior.coords)
        else:
            return None
        
        return {
            'id': str(uuid.uuid4()),
            'name': 'difference_region',
            'description': '差集区域',
            'type': 'walkable',
            'polygon': result_coords,
            'area': result_polygon.area,
            'bounds': result_polygon.bounds,
            'operation': 'difference',
            'source_regions': [target_region_id],
            'subtracted_regions': [r['id'] for r in other_regions]
        }
    
    def _symmetric_difference_operation(self, regions: List[Dict]) -> Dict:
        """对称差集操作"""
        if len(regions) != 2:
            return None
        
        polygon1 = Polygon(regions[0]['polygon'])
        polygon2 = Polygon(regions[1]['polygon'])
        
        if not polygon1.is_valid or not polygon2.is_valid:
            return None
        
        result_polygon = polygon1.symmetric_difference(polygon2)
        
        if result_polygon.is_empty:
            return None
        
        if result_polygon.geom_type == 'Polygon':
            result_coords = list(result_polygon.exterior.coords)
        elif result_polygon.geom_type == 'MultiPolygon':
            # 取最大的多边形
            largest_polygon = max(result_polygon.geoms, key=lambda p: p.area)
            result_coords = list(largest_polygon.exterior.coords)
        else:
            return None
        
        return {
            'id': str(uuid.uuid4()),
            'name': 'symmetric_difference_region',
            'description': '对称差集区域',
            'type': 'walkable',
            'polygon': result_coords,
            'area': result_polygon.area,
            'bounds': result_polygon.bounds,
            'operation': 'symmetric_difference',
            'source_regions': [r['id'] for r in regions]
        }
    
    def _subtract_obstacles(self, regions: List[Dict]) -> Dict:
        """从可寻路区域中减去障碍物区域"""
        print(f"布尔运算输入: {len(regions)} 个区域")

        # 分离可寻路区域和障碍物区域
        walkable_regions = [r for r in regions if r.get('type') == 'walkable']
        obstacle_regions = [r for r in regions if r.get('type') == 'obstacle']

        print(f"可行走区域: {len(walkable_regions)} 个")
        print(f"障碍物区域: {len(obstacle_regions)} 个")

        if not walkable_regions:
            print("错误: 没有可行走区域")
            return None

        # 合并所有可寻路区域
        walkable_polygons = []
        for i, region in enumerate(walkable_regions):
            try:
                # 确保多边形闭合
                polygon_coords = region['polygon']
                if polygon_coords[0] != polygon_coords[-1]:
                    polygon_coords = polygon_coords + [polygon_coords[0]]

                polygon = Polygon(polygon_coords)
                print(f"可行走区域 {i}: 有效={polygon.is_valid}, 面积={polygon.area}")

                if polygon.is_valid:
                    walkable_polygons.append(polygon)
                else:
                    # 尝试修复无效多边形
                    fixed_polygon = polygon.buffer(0)
                    if fixed_polygon.is_valid:
                        print(f"修复了无效的可行走区域 {i}")
                        walkable_polygons.append(fixed_polygon)
            except Exception as e:
                print(f"处理可行走区域 {i} 时出错: {e}")
                continue

        if not walkable_polygons:
            print("错误: 没有有效的可行走区域")
            return None

        merged_walkable = unary_union(walkable_polygons)
        print(f"合并后的可行走区域: 面积={merged_walkable.area}")

        # 从可寻路区域中减去障碍物
        result_polygon = merged_walkable
        for i, region in enumerate(obstacle_regions):
            try:
                # 确保多边形闭合
                polygon_coords = region['polygon']
                if polygon_coords[0] != polygon_coords[-1]:
                    polygon_coords = polygon_coords + [polygon_coords[0]]

                obstacle_polygon = Polygon(polygon_coords)
                print(f"障碍物区域 {i}: 有效={obstacle_polygon.is_valid}, 面积={obstacle_polygon.area}")

                if obstacle_polygon.is_valid:
                    old_area = result_polygon.area
                    result_polygon = result_polygon.difference(obstacle_polygon)
                    new_area = result_polygon.area if not result_polygon.is_empty else 0
                    print(f"减去障碍物 {i} 后: 面积从 {old_area} 变为 {new_area}")
                else:
                    # 尝试修复无效多边形
                    fixed_polygon = obstacle_polygon.buffer(0)
                    if fixed_polygon.is_valid:
                        print(f"修复了无效的障碍物区域 {i}")
                        old_area = result_polygon.area
                        result_polygon = result_polygon.difference(fixed_polygon)
                        new_area = result_polygon.area if not result_polygon.is_empty else 0
                        print(f"减去修复的障碍物 {i} 后: 面积从 {old_area} 变为 {new_area}")
            except Exception as e:
                print(f"处理障碍物区域 {i} 时出错: {e}")
                continue

        print(f"最终结果: 空={result_polygon.is_empty}, 类型={result_polygon.geom_type}")

        if result_polygon.is_empty:
            print("布尔运算结果为空")
            return None
        
        # 处理结果
        if result_polygon.geom_type == 'Polygon':
            result_coords = list(result_polygon.exterior.coords)
        elif result_polygon.geom_type == 'MultiPolygon':
            # 取最大的多边形
            largest_polygon = max(result_polygon.geoms, key=lambda p: p.area)
            result_coords = list(largest_polygon.exterior.coords)
        else:
            return None
        
        return {
            'id': str(uuid.uuid4()),
            'name': 'final_walkable_region',
            'description': '最终可寻路区域',
            'type': 'walkable',
            'polygon': result_coords,
            'area': result_polygon.area,
            'bounds': result_polygon.bounds,
            'operation': 'subtract_obstacles',
            'source_regions': [r['id'] for r in walkable_regions],
            'subtracted_regions': [r['id'] for r in obstacle_regions]
        }
    
    def validate_operation(self, operation: str, regions: List[Dict]) -> Dict:
        """验证布尔运算的可行性"""
        if not regions:
            return {'valid': False, 'error': '没有提供区域'}
        
        # 验证所有区域都是有效的多边形
        valid_regions = []
        for region in regions:
            if 'polygon' in region:
                try:
                    polygon = Polygon(region['polygon'])
                    if polygon.is_valid:
                        valid_regions.append(region)
                except:
                    continue
        
        if not valid_regions:
            return {'valid': False, 'error': '没有有效的区域'}
        
        # 根据操作类型进行特定验证
        if operation == "intersection" and len(valid_regions) < 2:
            return {'valid': False, 'error': '相交操作至少需要2个区域'}
        
        if operation == "difference" and len(valid_regions) < 2:
            return {'valid': False, 'error': '差集操作至少需要2个区域'}
        
        return {'valid': True, 'valid_regions': len(valid_regions)}


