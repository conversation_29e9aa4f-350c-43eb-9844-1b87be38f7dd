#!/usr/bin/env python3
"""
测试图片上传功能
"""

import requests
import os
from PIL import Image
import io

def create_test_image():
    """创建一个测试图片"""
    # 创建一个简单的测试图片
    img = Image.new('RGB', (100, 100), color='red')
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='PNG')
    img_bytes.seek(0)
    return img_bytes

def test_upload():
    """测试上传功能"""
    project_id = "e8a31bb8-3584-429f-82b7-feab15ff2583"
    url = f"http://localhost:5000/api/projects/{project_id}/images"
    
    # 创建测试图片
    img_data = create_test_image()
    
    try:
        files = {
            'image': ('test_image.png', img_data, 'image/png')
        }
        
        print(f"上传到: {url}")
        response = requests.post(url, files=files)
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print("上传成功!")
            print(f"图片ID: {result.get('id')}")
            print(f"文件名: {result.get('filename')}")
        else:
            print("上传失败!")
            
    except Exception as e:
        print(f"请求异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_upload()
