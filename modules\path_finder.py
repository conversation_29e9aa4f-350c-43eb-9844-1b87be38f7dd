"""
路径规划模块
基于网格拓扑实现A*寻路算法
"""

import math
import heapq
from typing import Dict, List, Optional, Tuple
from collections import defaultdict

class PathFinder:
    def __init__(self):
        self.paths = {}
    
    def find_path(self, start_point: Tuple[float, float], 
                 goal_point: Tuple[float, float], 
                 grid_data: Dict, 
                 algorithm: str = "astar") -> Dict:
        """寻找路径"""
        if not grid_data or 'nodes' not in grid_data or 'adjacency' not in grid_data:
            return {'path': [], 'error': '无效的网格数据'}
        
        # 找到最近的网格节点
        start_node = self._find_nearest_node(start_point, grid_data)
        goal_node = self._find_nearest_node(goal_point, grid_data)
        
        if start_node is None or goal_node is None:
            return {'path': [], 'error': '无法找到起点或终点的最近节点'}
        
        if start_node == goal_node:
            return {
                'path': [start_point, goal_point],
                'distance': 0,
                'nodes_used': [start_node],
                'algorithm': algorithm
            }
        
        # 执行寻路算法
        if algorithm == "astar":
            path_nodes = self._astar(start_node, goal_node, grid_data)
        elif algorithm == "dijkstra":
            path_nodes = self._dijkstra(start_node, goal_node, grid_data)
        else:
            return {'path': [], 'error': f'不支持的算法: {algorithm}'}
        
        if not path_nodes:
            return {'path': [], 'error': '无法找到路径'}
        
        # 转换为坐标路径
        path_coords = []
        for node_index in path_nodes:
            if node_index < len(grid_data['nodes']):
                path_coords.append(grid_data['nodes'][node_index])
        
        # 添加起点和终点（如果不在网格节点上）
        if start_node != self._find_exact_node(start_point, grid_data):
            path_coords.insert(0, start_point)
        if goal_node != self._find_exact_node(goal_point, grid_data):
            path_coords.append(goal_point)
        
        # 计算路径距离
        total_distance = self._calculate_path_distance(path_coords)
        
        return {
            'path': path_coords,
            'distance': total_distance,
            'nodes_used': path_nodes,
            'algorithm': algorithm,
            'start_node': start_node,
            'goal_node': goal_node
        }
    
    def _find_nearest_node(self, point: Tuple[float, float], 
                          grid_data: Dict) -> Optional[int]:
        """找到最近的网格节点"""
        nodes = grid_data['nodes']
        if not nodes:
            return None
        
        min_distance = float('inf')
        nearest_node = None
        
        for i, node in enumerate(nodes):
            distance = self._haversine_distance(point, node)
            if distance < min_distance:
                min_distance = distance
                nearest_node = i
        
        return nearest_node
    
    def _find_exact_node(self, point: Tuple[float, float], 
                        grid_data: Dict) -> Optional[int]:
        """找到精确匹配的网格节点"""
        nodes = grid_data['nodes']
        if not nodes:
            return None
        
        for i, node in enumerate(nodes):
            if abs(point[0] - node[0]) < 1e-9 and abs(point[1] - node[1]) < 1e-9:
                return i
        
        return None
    
    def _astar(self, start: int, goal: int, grid_data: Dict) -> List[int]:
        """A*寻路算法"""
        nodes = grid_data['nodes']
        adjacency = grid_data['adjacency']
        
        if start not in adjacency or goal not in adjacency:
            return []
        
        # 优先队列：(f_score, node)
        open_set = [(0, start)]
        heapq.heapify(open_set)
        
        # 记录路径
        came_from = {}
        
        # g_score: 从起点到当前节点的实际距离
        g_score = defaultdict(lambda: float('inf'))
        g_score[start] = 0
        
        # f_score: g_score + 启发式距离
        f_score = defaultdict(lambda: float('inf'))
        f_score[start] = self._heuristic(start, goal, nodes)
        
        # 已访问的节点
        visited = set()
        
        while open_set:
            current_f, current = heapq.heappop(open_set)
            
            if current in visited:
                continue
            
            visited.add(current)
            
            if current == goal:
                # 重构路径
                path = []
                while current in came_from:
                    path.append(current)
                    current = came_from[current]
                path.append(start)
                return path[::-1]
            
            # 检查邻居
            for neighbor in adjacency.get(current, []):
                if neighbor in visited:
                    continue
                
                # 计算到邻居的距离
                tentative_g = g_score[current] + self._haversine_distance(
                    nodes[current], nodes[neighbor]
                )
                
                if tentative_g < g_score[neighbor]:
                    came_from[neighbor] = current
                    g_score[neighbor] = tentative_g
                    f_score[neighbor] = tentative_g + self._heuristic(neighbor, goal, nodes)
                    
                    heapq.heappush(open_set, (f_score[neighbor], neighbor))
        
        return []  # 没有找到路径
    
    def _dijkstra(self, start: int, goal: int, grid_data: Dict) -> List[int]:
        """Dijkstra寻路算法"""
        nodes = grid_data['nodes']
        adjacency = grid_data['adjacency']
        
        if start not in adjacency or goal not in adjacency:
            return []
        
        # 优先队列：(distance, node)
        open_set = [(0, start)]
        heapq.heapify(open_set)
        
        # 记录路径
        came_from = {}
        
        # 距离记录
        distances = defaultdict(lambda: float('inf'))
        distances[start] = 0
        
        # 已访问的节点
        visited = set()
        
        while open_set:
            current_dist, current = heapq.heappop(open_set)
            
            if current in visited:
                continue
            
            visited.add(current)
            
            if current == goal:
                # 重构路径
                path = []
                while current in came_from:
                    path.append(current)
                    current = came_from[current]
                path.append(start)
                return path[::-1]
            
            # 检查邻居
            for neighbor in adjacency.get(current, []):
                if neighbor in visited:
                    continue
                
                # 计算到邻居的距离
                distance = current_dist + self._haversine_distance(
                    nodes[current], nodes[neighbor]
                )
                
                if distance < distances[neighbor]:
                    came_from[neighbor] = current
                    distances[neighbor] = distance
                    
                    heapq.heappush(open_set, (distance, neighbor))
        
        return []  # 没有找到路径
    
    def _heuristic(self, node1: int, node2: int, nodes: List[List[float]]) -> float:
        """启发式函数（直线距离）"""
        return self._haversine_distance(nodes[node1], nodes[node2])
    
    def _haversine_distance(self, point1: Tuple[float, float], 
                           point2: Tuple[float, float]) -> float:
        """计算两点间的距离（米）"""
        lat1, lon1 = point1
        lat2, lon2 = point2
        
        R = 6371000  # 地球半径（米）
        phi1 = math.radians(lat1)
        phi2 = math.radians(lat2)
        dphi = math.radians(lat2 - lat1)
        dlambda = math.radians(lon2 - lon1)
        
        a = (math.sin(dphi/2)**2 + 
             math.cos(phi1) * math.cos(phi2) * math.sin(dlambda/2)**2)
        
        return 2 * R * math.atan2(math.sqrt(a), math.sqrt(1-a))
    
    def _calculate_path_distance(self, path: List[Tuple[float, float]]) -> float:
        """计算路径总距离"""
        if len(path) < 2:
            return 0
        
        total_distance = 0
        for i in range(len(path) - 1):
            total_distance += self._haversine_distance(path[i], path[i + 1])
        
        return total_distance
    
    def optimize_path(self, path: List[Tuple[float, float]], 
                     grid_data: Dict) -> List[Tuple[float, float]]:
        """优化路径（简化不必要的转折点）"""
        if len(path) < 3:
            return path
        
        optimized_path = [path[0]]
        
        for i in range(1, len(path) - 1):
            # 检查是否可以跳过当前点
            prev_point = optimized_path[-1]
            next_point = path[i + 1]
            current_point = path[i]
            
            # 如果三点共线或接近共线，跳过中间点
            if not self._is_significant_turn(prev_point, current_point, next_point):
                continue
            
            optimized_path.append(current_point)
        
        optimized_path.append(path[-1])
        return optimized_path
    
    def _is_significant_turn(self, p1: Tuple[float, float], 
                           p2: Tuple[float, float], 
                           p3: Tuple[float, float], 
                           threshold: float = 15.0) -> bool:
        """判断是否是有意义的转向（角度大于阈值）"""
        # 计算角度
        angle = self._calculate_angle(p1, p2, p3)
        return abs(angle) > threshold
    
    def _calculate_angle(self, p1: Tuple[float, float], 
                        p2: Tuple[float, float], 
                        p3: Tuple[float, float]) -> float:
        """计算三点形成的角度"""
        # 向量计算
        v1 = (p1[0] - p2[0], p1[1] - p2[1])
        v2 = (p3[0] - p2[0], p3[1] - p2[1])
        
        # 计算角度
        dot_product = v1[0] * v2[0] + v1[1] * v2[1]
        mag1 = math.sqrt(v1[0]**2 + v1[1]**2)
        mag2 = math.sqrt(v2[0]**2 + v2[1]**2)
        
        if mag1 == 0 or mag2 == 0:
            return 0
        
        cos_angle = dot_product / (mag1 * mag2)
        cos_angle = max(-1, min(1, cos_angle))  # 防止数值误差
        
        angle = math.degrees(math.acos(cos_angle))
        
        # 判断转向方向
        cross_product = v1[0] * v2[1] - v1[1] * v2[0]
        if cross_product < 0:
            angle = -angle
        
        return angle


