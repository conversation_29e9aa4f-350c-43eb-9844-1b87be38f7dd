"""
网格生成模块
基于可寻路区域生成网格拓扑
"""

import math
import uuid
from typing import Dict, List, Tuple, Optional
from shapely.geometry import Polygon, Point

class GridGenerator:
    def __init__(self):
        self.grids = {}
    
    def generate_grid(self, walkable_regions: List[Dict],
                     grid_size: float = 50.0, obstacle_regions: List[Dict] = None) -> Dict:
        """生成网格拓扑"""
        if not walkable_regions:
            return None

        grid_id = str(uuid.uuid4())

        # 合并所有可寻路区域
        merged_walkable = self._merge_walkable_regions(walkable_regions)
        if not merged_walkable:
            return None

        # 处理障碍物区域
        final_walkable = merged_walkable
        if obstacle_regions:
            obstacle_polygons = []
            for region in obstacle_regions:
                if 'polygon' in region and region.get('type') == 'obstacle':
                    try:
                        obstacle_poly = Polygon(region['polygon'])
                        if obstacle_poly.is_valid:
                            obstacle_polygons.append(obstacle_poly)
                    except:
                        continue

            # 从可行走区域中减去障碍物
            if obstacle_polygons:
                from shapely.ops import unary_union
                merged_obstacles = unary_union(obstacle_polygons)
                final_walkable = final_walkable.difference(merged_obstacles)

                # 处理结果可能是MultiPolygon的情况
                if final_walkable.geom_type == 'MultiPolygon':
                    # 选择最大的多边形
                    final_walkable = max(final_walkable.geoms, key=lambda p: p.area)
                elif final_walkable.is_empty:
                    return None

        # 计算网格边界
        bounds = final_walkable.bounds  # (minx, miny, maxx, maxy)
        print(f"多边形边界: minx={bounds[0]}, miny={bounds[1]}, maxx={bounds[2]}, maxy={bounds[3]}")

        # 计算网格步长（转换为经纬度）
        step_lat, step_lon = self._calculate_grid_step(bounds, grid_size)
        print(f"网格步长: step_lat={step_lat}, step_lon={step_lon}")

        # 生成网格节点 - 改进的网格生成算法
        nodes = []
        node_positions = {}  # (i, j) -> node_index

        # 计算预期的网格大小，避免生成过多节点
        lat_steps = int((bounds[3] - bounds[1]) / step_lat) + 1
        lon_steps = int((bounds[2] - bounds[0]) / step_lon) + 1
        max_nodes = lat_steps * lon_steps

        print(f"预期网格大小: {lat_steps} x {lon_steps} = {max_nodes} 个节点")

        if max_nodes > 10000:  # 限制最大节点数
            print(f"网格太密集 ({max_nodes} 节点)，调整网格大小")
            # 自动调整网格大小
            scale_factor = (max_nodes / 10000) ** 0.5
            step_lat *= scale_factor
            step_lon *= scale_factor
            print(f"调整后的步长: lat={step_lat}, lon={step_lon}")

        # 使用更精确的网格生成
        i = 0
        lat = bounds[1]  # min_lat
        processed_points = 0

        while lat <= bounds[3] and i < 200:  # 限制最大行数
            j = 0
            lon = bounds[0]  # min_lon

            while lon <= bounds[2] and j < 200:  # 限制最大列数
                # 创建点 (lon, lat) - Shapely使用 (x, y) 格式
                point = Point(lon, lat)
                processed_points += 1

                # 每处理1000个点输出一次进度
                if processed_points % 1000 == 0:
                    print(f"已处理 {processed_points} 个点，生成 {len(nodes)} 个节点")

                # 检查点是否在最终的可行走区域内
                try:
                    if final_walkable.contains(point):
                        node_index = len(nodes)
                        # 存储为 [lat, lon] 格式以保持一致性
                        nodes.append([lat, lon])
                        node_positions[(i, j)] = node_index

                        # 输出前几个有效节点用于调试
                        if len(nodes) <= 5:
                            print(f"生成节点 {len(nodes)}: lat={lat:.6f}, lon={lon:.6f}")

                except Exception as e:
                    # 如果点包含检查失败，跳过这个点
                    if processed_points <= 10:  # 只在前几个点输出错误
                        print(f"点包含检查失败 (lat={lat:.6f}, lon={lon:.6f}): {e}")
                    continue

                lon += step_lon
                j += 1
            lat += step_lat
            i += 1

        print(f"网格生成完成: 处理了 {processed_points} 个点，生成了 {len(nodes)} 个有效节点")
        
        # 构建邻接关系
        adjacency = self._build_adjacency(node_positions, i, j)
        
        grid_data = {
            'id': grid_id,
            'grid_size': grid_size,
            'bounds': bounds,
            'step_lat': step_lat,
            'step_lon': step_lon,
            'nodes': nodes,
            'adjacency': adjacency,
            'node_positions': node_positions,
            'total_nodes': len(nodes),
            'total_connections': sum(len(neighbors) for neighbors in adjacency.values())
        }
        
        self.grids[grid_id] = grid_data
        return grid_data
    
    def _merge_walkable_regions(self, walkable_regions: List[Dict]) -> Optional[Polygon]:
        """合并可寻路区域，支持带洞的多边形"""
        polygons = []

        for region in walkable_regions:
            if 'polygon' in region:
                try:
                    # 检查是否是布尔运算的结果（带洞的多边形）
                    if region.get('has_holes') and region.get('holes'):
                        # 创建带洞的多边形
                        exterior_coords = region['polygon']
                        holes = region['holes']

                        print(f"处理带洞多边形: 外边界={len(exterior_coords)}点, 洞={len(holes)}个")
                        print(f"外边界: {exterior_coords[:3]}...")  # 显示前3个点
                        for i, hole in enumerate(holes):
                            print(f"洞{i}: {len(hole)}点, {hole[:3]}...")

                        polygon = Polygon(exterior_coords, holes)

                        if polygon.is_valid:
                            polygons.append(polygon)
                            print(f"带洞多边形有效，面积={polygon.area:.6f}")
                        else:
                            print("带洞多边形无效，尝试修复")
                            fixed = polygon.buffer(0)
                            if fixed.is_valid and not fixed.is_empty:
                                polygons.append(fixed)
                                print(f"修复后的多边形面积={fixed.area:.6f}")
                            else:
                                print("无法修复带洞多边形")

                    elif region.get('is_multipolygon') and region.get('all_polygons'):
                        # 处理多个多边形的情况
                        for poly_info in region['all_polygons']:
                            exterior_coords = poly_info['polygon']
                            holes = poly_info.get('holes', [])

                            if holes:
                                polygon = Polygon(exterior_coords, holes)
                            else:
                                polygon = Polygon(exterior_coords)

                            if polygon.is_valid:
                                polygons.append(polygon)

                    else:
                        # 普通多边形
                        polygon = Polygon(region['polygon'])
                        if polygon.is_valid:
                            polygons.append(polygon)

                except Exception as e:
                    print(f"处理可行走区域时出错: {e}")
                    continue

        if not polygons:
            return None

        # 合并所有多边形
        from shapely.ops import unary_union
        merged = unary_union(polygons)

        print(f"合并后的多边形类型: {merged.geom_type}, 面积: {merged.area}")

        if merged.geom_type == 'Polygon':
            return merged
        elif merged.geom_type == 'MultiPolygon':
            # 取最大的多边形
            largest = max(merged.geoms, key=lambda p: p.area)
            print(f"选择最大的多边形，面积: {largest.area}")
            return largest
        else:
            return None
    
    def _calculate_grid_step(self, bounds: Tuple[float, float, float, float],
                           grid_size: float) -> Tuple[float, float]:
        """计算网格步长"""
        # bounds格式: (minx, miny, maxx, maxy) = (min_lon, min_lat, max_lon, max_lat)

        # 将米转换为经纬度
        # 1度纬度约等于111320米
        step_lat = grid_size / 111320.0

        # 经度步长取决于纬度
        center_lat = (bounds[1] + bounds[3]) / 2  # (min_lat + max_lat) / 2
        # 1度经度在赤道处约等于111320米，在其他纬度处需要乘以cos(lat)
        meters_per_degree_lon = 111320.0 * math.cos(math.radians(center_lat))
        step_lon = grid_size / meters_per_degree_lon

        return step_lat, step_lon
    
    def _build_adjacency(self, node_positions: Dict[Tuple[int, int], int], 
                        max_i: int, max_j: int) -> Dict[int, List[int]]:
        """构建邻接关系"""
        adjacency = {}
        
        # 8方向连接（包括对角线）
        directions = [
            (-1, -1), (-1, 0), (-1, 1),
            (0, -1),           (0, 1),
            (1, -1),  (1, 0),  (1, 1)
        ]
        
        for (i, j), node_index in node_positions.items():
            neighbors = []
            
            for di, dj in directions:
                ni, nj = i + di, j + dj
                neighbor_index = node_positions.get((ni, nj))
                if neighbor_index is not None:
                    neighbors.append(neighbor_index)
            
            adjacency[node_index] = neighbors
        
        return adjacency
    
    def find_nearest_node(self, point: Tuple[float, float], 
                         grid_data: Dict) -> Optional[int]:
        """找到最近的网格节点"""
        if not grid_data or 'nodes' not in grid_data:
            return None
        
        min_distance = float('inf')
        nearest_node = None
        
        for i, node in enumerate(grid_data['nodes']):
            distance = self._haversine_distance(point, node)
            if distance < min_distance:
                min_distance = distance
                nearest_node = i
        
        return nearest_node
    
    def _haversine_distance(self, point1: Tuple[float, float], 
                           point2: Tuple[float, float]) -> float:
        """计算两点间的距离（米）"""
        lat1, lon1 = point1
        lat2, lon2 = point2
        
        R = 6371000  # 地球半径（米）
        phi1 = math.radians(lat1)
        phi2 = math.radians(lat2)
        dphi = math.radians(lat2 - lat1)
        dlambda = math.radians(lon2 - lon1)
        
        a = (math.sin(dphi/2)**2 + 
             math.cos(phi1) * math.cos(phi2) * math.sin(dlambda/2)**2)
        
        return 2 * R * math.atan2(math.sqrt(a), math.sqrt(1-a))
    
    def optimize_grid(self, grid_data: Dict) -> Dict:
        """优化网格（移除不必要的节点）"""
        if not grid_data or 'nodes' not in grid_data:
            return grid_data
        
        # 简化网格：移除度数小于3的节点（除非是起点或终点）
        optimized_adjacency = {}
        optimized_nodes = []
        node_mapping = {}  # 旧索引 -> 新索引
        
        # 保留所有节点，但优化连接
        for old_index, node in enumerate(grid_data['nodes']):
            neighbors = grid_data['adjacency'].get(old_index, [])
            
            # 只保留有效的连接
            valid_neighbors = []
            for neighbor in neighbors:
                if neighbor in grid_data['adjacency']:
                    valid_neighbors.append(neighbor)
            
            if valid_neighbors:  # 只保留有连接的节点
                new_index = len(optimized_nodes)
                optimized_nodes.append(node)
                optimized_adjacency[new_index] = valid_neighbors
                node_mapping[old_index] = new_index
        
        # 更新邻接关系中的索引
        for node_index, neighbors in optimized_adjacency.items():
            optimized_adjacency[node_index] = [
                node_mapping[neighbor] for neighbor in neighbors 
                if neighbor in node_mapping
            ]
        
        # 更新网格数据
        optimized_grid = grid_data.copy()
        optimized_grid['nodes'] = optimized_nodes
        optimized_grid['adjacency'] = optimized_adjacency
        optimized_grid['total_nodes'] = len(optimized_nodes)
        optimized_grid['total_connections'] = sum(
            len(neighbors) for neighbors in optimized_adjacency.values()
        )
        
        return optimized_grid
    
    def get_grid_statistics(self, grid_data: Dict) -> Dict:
        """获取网格统计信息"""
        if not grid_data:
            return {}
        
        nodes = grid_data.get('nodes', [])
        adjacency = grid_data.get('adjacency', {})
        
        # 计算连接度分布
        degrees = [len(neighbors) for neighbors in adjacency.values()]
        
        return {
            'total_nodes': len(nodes),
            'total_connections': sum(degrees),
            'average_degree': sum(degrees) / len(degrees) if degrees else 0,
            'max_degree': max(degrees) if degrees else 0,
            'min_degree': min(degrees) if degrees else 0,
            'grid_size': grid_data.get('grid_size', 0),
            'bounds': grid_data.get('bounds', [])
        }


