"""
网格生成模块
基于可寻路区域生成网格拓扑
"""

import math
import uuid
from typing import Dict, List, Tuple, Optional
from shapely.geometry import Polygon, Point

class GridGenerator:
    def __init__(self):
        self.grids = {}
    
    def generate_grid(self, walkable_regions: List[Dict], 
                     grid_size: float = 50.0) -> Dict:
        """生成网格拓扑"""
        if not walkable_regions:
            return None
        
        grid_id = str(uuid.uuid4())
        
        # 合并所有可寻路区域
        merged_polygon = self._merge_walkable_regions(walkable_regions)
        if not merged_polygon:
            return None
        
        # 计算网格边界
        bounds = merged_polygon.bounds  # (minx, miny, maxx, maxy)
        
        # 计算网格步长（转换为经纬度）
        step_lat, step_lon = self._calculate_grid_step(bounds, grid_size)
        
        # 生成网格节点
        nodes = []
        node_positions = {}  # (i, j) -> node_index
        
        i = 0
        lat = bounds[1]  # miny
        while lat <= bounds[3]:  # maxy
            j = 0
            lon = bounds[0]  # minx
            while lon <= bounds[2]:  # maxx
                point = Point(lon, lat)
                if merged_polygon.contains(point):
                    node_index = len(nodes)
                    nodes.append([lat, lon])
                    node_positions[(i, j)] = node_index
                lon += step_lon
                j += 1
            lat += step_lat
            i += 1
        
        # 构建邻接关系
        adjacency = self._build_adjacency(node_positions, i, j)
        
        grid_data = {
            'id': grid_id,
            'grid_size': grid_size,
            'bounds': bounds,
            'step_lat': step_lat,
            'step_lon': step_lon,
            'nodes': nodes,
            'adjacency': adjacency,
            'node_positions': node_positions,
            'total_nodes': len(nodes),
            'total_connections': sum(len(neighbors) for neighbors in adjacency.values())
        }
        
        self.grids[grid_id] = grid_data
        return grid_data
    
    def _merge_walkable_regions(self, walkable_regions: List[Dict]) -> Optional[Polygon]:
        """合并可寻路区域"""
        polygons = []
        
        for region in walkable_regions:
            if 'polygon' in region:
                try:
                    polygon = Polygon(region['polygon'])
                    if polygon.is_valid:
                        polygons.append(polygon)
                except:
                    continue
        
        if not polygons:
            return None
        
        # 合并所有多边形
        from shapely.ops import unary_union
        merged = unary_union(polygons)
        
        if merged.geom_type == 'Polygon':
            return merged
        elif merged.geom_type == 'MultiPolygon':
            # 取最大的多边形
            return max(merged.geoms, key=lambda p: p.area)
        else:
            return None
    
    def _calculate_grid_step(self, bounds: Tuple[float, float, float, float], 
                           grid_size: float) -> Tuple[float, float]:
        """计算网格步长"""
        # 将米转换为经纬度
        # 1度纬度约等于111320米
        step_lat = grid_size / 111320.0
        
        # 经度步长取决于纬度
        center_lat = (bounds[1] + bounds[3]) / 2
        step_lon = grid_size / (40075000 * math.cos(math.radians(center_lat)) / 360)
        
        return step_lat, step_lon
    
    def _build_adjacency(self, node_positions: Dict[Tuple[int, int], int], 
                        max_i: int, max_j: int) -> Dict[int, List[int]]:
        """构建邻接关系"""
        adjacency = {}
        
        # 8方向连接（包括对角线）
        directions = [
            (-1, -1), (-1, 0), (-1, 1),
            (0, -1),           (0, 1),
            (1, -1),  (1, 0),  (1, 1)
        ]
        
        for (i, j), node_index in node_positions.items():
            neighbors = []
            
            for di, dj in directions:
                ni, nj = i + di, j + dj
                neighbor_index = node_positions.get((ni, nj))
                if neighbor_index is not None:
                    neighbors.append(neighbor_index)
            
            adjacency[node_index] = neighbors
        
        return adjacency
    
    def find_nearest_node(self, point: Tuple[float, float], 
                         grid_data: Dict) -> Optional[int]:
        """找到最近的网格节点"""
        if not grid_data or 'nodes' not in grid_data:
            return None
        
        min_distance = float('inf')
        nearest_node = None
        
        for i, node in enumerate(grid_data['nodes']):
            distance = self._haversine_distance(point, node)
            if distance < min_distance:
                min_distance = distance
                nearest_node = i
        
        return nearest_node
    
    def _haversine_distance(self, point1: Tuple[float, float], 
                           point2: Tuple[float, float]) -> float:
        """计算两点间的距离（米）"""
        lat1, lon1 = point1
        lat2, lon2 = point2
        
        R = 6371000  # 地球半径（米）
        phi1 = math.radians(lat1)
        phi2 = math.radians(lat2)
        dphi = math.radians(lat2 - lat1)
        dlambda = math.radians(lon2 - lon1)
        
        a = (math.sin(dphi/2)**2 + 
             math.cos(phi1) * math.cos(phi2) * math.sin(dlambda/2)**2)
        
        return 2 * R * math.atan2(math.sqrt(a), math.sqrt(1-a))
    
    def optimize_grid(self, grid_data: Dict) -> Dict:
        """优化网格（移除不必要的节点）"""
        if not grid_data or 'nodes' not in grid_data:
            return grid_data
        
        # 简化网格：移除度数小于3的节点（除非是起点或终点）
        optimized_adjacency = {}
        optimized_nodes = []
        node_mapping = {}  # 旧索引 -> 新索引
        
        # 保留所有节点，但优化连接
        for old_index, node in enumerate(grid_data['nodes']):
            neighbors = grid_data['adjacency'].get(old_index, [])
            
            # 只保留有效的连接
            valid_neighbors = []
            for neighbor in neighbors:
                if neighbor in grid_data['adjacency']:
                    valid_neighbors.append(neighbor)
            
            if valid_neighbors:  # 只保留有连接的节点
                new_index = len(optimized_nodes)
                optimized_nodes.append(node)
                optimized_adjacency[new_index] = valid_neighbors
                node_mapping[old_index] = new_index
        
        # 更新邻接关系中的索引
        for node_index, neighbors in optimized_adjacency.items():
            optimized_adjacency[node_index] = [
                node_mapping[neighbor] for neighbor in neighbors 
                if neighbor in node_mapping
            ]
        
        # 更新网格数据
        optimized_grid = grid_data.copy()
        optimized_grid['nodes'] = optimized_nodes
        optimized_grid['adjacency'] = optimized_adjacency
        optimized_grid['total_nodes'] = len(optimized_nodes)
        optimized_grid['total_connections'] = sum(
            len(neighbors) for neighbors in optimized_adjacency.values()
        )
        
        return optimized_grid
    
    def get_grid_statistics(self, grid_data: Dict) -> Dict:
        """获取网格统计信息"""
        if not grid_data:
            return {}
        
        nodes = grid_data.get('nodes', [])
        adjacency = grid_data.get('adjacency', {})
        
        # 计算连接度分布
        degrees = [len(neighbors) for neighbors in adjacency.values()]
        
        return {
            'total_nodes': len(nodes),
            'total_connections': sum(degrees),
            'average_degree': sum(degrees) / len(degrees) if degrees else 0,
            'max_degree': max(degrees) if degrees else 0,
            'min_degree': min(degrees) if degrees else 0,
            'grid_size': grid_data.get('grid_size', 0),
            'bounds': grid_data.get('bounds', [])
        }


