"""
区域编辑模块
负责机场区域的描边、编辑、多边形操作
"""

import uuid
import math
from typing import Dict, List, Optional, Tuple
from shapely.geometry import Polygon, Point
from shapely.ops import unary_union

class RegionEditor:
    def __init__(self):
        self.regions = {}
    
    def create_region(self, polygon: List[List[float]], 
                     region_type: str = "airport") -> Dict:
        """创建新区域"""
        region_id = str(uuid.uuid4())
        
        # 验证多边形
        if len(polygon) < 3:
            raise ValueError("多边形至少需要3个点")
        
        # 确保多边形闭合
        if polygon[0] != polygon[-1]:
            polygon.append(polygon[0])
        
        # 创建Shapely多边形对象
        shapely_polygon = Polygon(polygon)
        
        if not shapely_polygon.is_valid:
            raise ValueError("无效的多边形")
        
        region_data = {
            'id': region_id,
            'name': f"{region_type}_region_{region_id[:8]}",
            'description': f"机场{region_type}区域",
            'type': region_type,
            'polygon': polygon,
            'shapely_polygon': shapely_polygon,
            'area': shapely_polygon.area,
            'bounds': shapely_polygon.bounds,
            'created_at': str(uuid.uuid4())  # 简化时间戳
        }
        
        self.regions[region_id] = region_data
        return region_data
    
    def update_region(self, region_id: str, update_data: Dict) -> Optional[Dict]:
        """更新区域"""
        if region_id not in self.regions:
            return None
        
        region = self.regions[region_id]
        
        # 更新多边形
        if 'polygon' in update_data:
            polygon = update_data['polygon']
            if len(polygon) < 3:
                raise ValueError("多边形至少需要3个点")
            
            # 确保多边形闭合
            if polygon[0] != polygon[-1]:
                polygon.append(polygon[0])
            
            # 创建新的Shapely多边形
            shapely_polygon = Polygon(polygon)
            if not shapely_polygon.is_valid:
                raise ValueError("无效的多边形")
            
            region['polygon'] = polygon
            region['shapely_polygon'] = shapely_polygon
            region['area'] = shapely_polygon.area
            region['bounds'] = shapely_polygon.bounds
        
        # 更新其他属性
        if 'name' in update_data:
            region['name'] = update_data['name']
        if 'description' in update_data:
            region['description'] = update_data['description']
        if 'type' in update_data:
            region['type'] = update_data['type']
        
        return region
    
    def delete_region(self, region_id: str) -> bool:
        """删除区域"""
        if region_id in self.regions:
            del self.regions[region_id]
            return True
        return False
    
    def get_region(self, region_id: str) -> Optional[Dict]:
        """获取区域"""
        return self.regions.get(region_id)
    
    def point_in_region(self, point: Tuple[float, float], region_id: str) -> bool:
        """判断点是否在区域内"""
        region = self.get_region(region_id)
        if not region:
            return False
        
        shapely_point = Point(point[1], point[0])  # 注意坐标顺序
        return region['shapely_polygon'].contains(shapely_point)
    
    def region_intersects(self, region_id1: str, region_id2: str) -> bool:
        """判断两个区域是否相交"""
        region1 = self.get_region(region_id1)
        region2 = self.get_region(region_id2)
        
        if not region1 or not region2:
            return False
        
        return region1['shapely_polygon'].intersects(region2['shapely_polygon'])
    
    def merge_regions(self, region_ids: List[str]) -> Optional[Dict]:
        """合并多个区域"""
        if len(region_ids) < 2:
            return None
        
        polygons = []
        for region_id in region_ids:
            region = self.get_region(region_id)
            if region:
                polygons.append(region['shapely_polygon'])
        
        if not polygons:
            return None
        
        # 合并多边形
        merged_polygon = unary_union(polygons)
        
        # 创建新区域
        if merged_polygon.geom_type == 'Polygon':
            polygon_coords = list(merged_polygon.exterior.coords)
        else:
            # 如果是MultiPolygon，取最大的部分
            largest_polygon = max(merged_polygon.geoms, key=lambda p: p.area)
            polygon_coords = list(largest_polygon.exterior.coords)
        
        return self.create_region(polygon_coords, "merged")
    
    def simplify_region(self, region_id: str, tolerance: float = 0.0001) -> Optional[Dict]:
        """简化区域边界"""
        region = self.get_region(region_id)
        if not region:
            return None
        
        # 简化多边形
        simplified_polygon = region['shapely_polygon'].simplify(tolerance)
        
        if simplified_polygon.geom_type == 'Polygon':
            polygon_coords = list(simplified_polygon.exterior.coords)
        else:
            return None
        
        # 更新区域
        return self.update_region(region_id, {'polygon': polygon_coords})
    
    def calculate_region_area(self, region_id: str) -> Optional[float]:
        """计算区域面积（平方米）"""
        region = self.get_region(region_id)
        if not region:
            return None
        
        # 这里需要根据实际坐标系统计算真实面积
        # 简化实现，返回相对面积
        return region['area']
    
    def get_region_center(self, region_id: str) -> Optional[Tuple[float, float]]:
        """获取区域中心点"""
        region = self.get_region(region_id)
        if not region:
            return None
        
        centroid = region['shapely_polygon'].centroid
        return (centroid.y, centroid.x)  # 返回 (lat, lng)
    
    def validate_polygon(self, polygon: List[List[float]]) -> Dict:
        """验证多边形有效性"""
        try:
            if len(polygon) < 3:
                return {'valid': False, 'error': '多边形至少需要3个点'}
            
            # 确保多边形闭合
            if polygon[0] != polygon[-1]:
                polygon.append(polygon[0])
            
            shapely_polygon = Polygon(polygon)
            
            if not shapely_polygon.is_valid:
                return {'valid': False, 'error': '无效的多边形几何'}
            
            return {
                'valid': True,
                'area': shapely_polygon.area,
                'bounds': shapely_polygon.bounds
            }
        
        except Exception as e:
            return {'valid': False, 'error': str(e)}


