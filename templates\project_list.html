<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>机场导航规划平台 - 项目管理</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 1.5rem;
            font-weight: 700;
            color: #2c3e50;
        }

        .logo i {
            color: #3498db;
            font-size: 2rem;
        }

        .header-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.9);
            color: #2c3e50;
            border: 2px solid #e0e0e0;
        }

        .btn-secondary:hover {
            background: white;
            border-color: #3498db;
            color: #3498db;
        }

        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .page-title {
            text-align: center;
            margin-bottom: 3rem;
        }

        .page-title h1 {
            font-size: 2.5rem;
            color: white;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .page-title p {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.1rem;
        }

        .projects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .project-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .project-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #3498db, #2ecc71);
        }

        .project-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
        }

        .project-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .project-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .project-description {
            color: #7f8c8d;
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        .project-meta {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            margin-bottom: 1.5rem;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
            color: #95a5a6;
        }

        .meta-item i {
            width: 16px;
            color: #3498db;
        }

        .project-actions {
            display: flex;
            gap: 0.5rem;
            justify-content: flex-end;
        }

        .btn-small {
            padding: 0.5rem 1rem;
            font-size: 0.8rem;
            border-radius: 6px;
        }

        .btn-edit {
            background: #3498db;
            color: white;
        }

        .btn-edit:hover {
            background: #2980b9;
        }

        .btn-delete {
            background: #e74c3c;
            color: white;
        }

        .btn-delete:hover {
            background: #c0392b;
        }

        .create-project-card {
            background: rgba(255, 255, 255, 0.1);
            border: 2px dashed rgba(255, 255, 255, 0.3);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: white;
            min-height: 250px;
        }

        .create-project-card:hover {
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .create-project-card i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.7;
        }

        .create-project-card h3 {
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
        }

        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            color: white;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 2rem;
            opacity: 0.5;
        }

        .empty-state h2 {
            font-size: 2rem;
            margin-bottom: 1rem;
        }

        .empty-state p {
            font-size: 1.1rem;
            opacity: 0.8;
            margin-bottom: 2rem;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 2rem;
            border-radius: 16px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        .modal-header h2 {
            color: #2c3e50;
            font-size: 1.5rem;
        }

        .close {
            font-size: 2rem;
            cursor: pointer;
            color: #95a5a6;
        }

        .close:hover {
            color: #e74c3c;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #3498db;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }

        .coord-inputs {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        @media (max-width: 768px) {
            .projects-grid {
                grid-template-columns: 1fr;
            }
            
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }
            
            .coord-inputs {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-plane"></i>
                <span>机场导航规划平台</span>
            </div>
            <div class="header-actions">
                <button class="btn btn-primary" onclick="showCreateModal()">
                    <i class="fas fa-plus"></i>
                    新建项目
                </button>
                <a href="/help" class="btn btn-secondary">
                    <i class="fas fa-question-circle"></i>
                    帮助
                </a>
            </div>
        </div>
    </header>

    <main class="main-container">
        <div class="page-title">
            <h1>项目管理</h1>
            <p>创建和管理您的机场导航规划项目</p>
        </div>

        <div id="projects-container">
            <!-- 项目列表将在这里动态加载 -->
        </div>
    </main>

    <!-- 创建项目模态框 -->
    <div id="createModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>创建新项目</h2>
                <span class="close" onclick="hideCreateModal()">&times;</span>
            </div>
            <form id="createProjectForm">
                <div class="form-group">
                    <label for="projectName">项目名称 *</label>
                    <input type="text" id="projectName" name="name" required>
                </div>
                <div class="form-group">
                    <label for="projectDescription">项目描述</label>
                    <textarea id="projectDescription" name="description" placeholder="请输入项目描述..."></textarea>
                </div>
                <div class="form-group">
                    <label>机场坐标</label>
                    <div class="coord-inputs">
                        <div>
                            <label for="airportLat">纬度</label>
                            <input type="number" id="airportLat" name="lat" step="0.000001" value="30.314">
                        </div>
                        <div>
                            <label for="airportLng">经度</label>
                            <input type="number" id="airportLng" name="lng" step="0.000001" value="104.445">
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <button type="submit" class="btn btn-primary" style="width: 100%;">
                        <i class="fas fa-plus"></i>
                        创建项目
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // 全局变量
        let projects = [];

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadProjects();
        });

        // 加载项目列表
        async function loadProjects() {
            try {
                const response = await fetch('/api/projects');
                if (response.ok) {
                    projects = await response.json();
                    renderProjects();
                } else {
                    console.error('加载项目失败');
                    renderEmptyState();
                }
            } catch (error) {
                console.error('加载项目出错:', error);
                renderEmptyState();
            }
        }

        // 渲染项目列表
        function renderProjects() {
            const container = document.getElementById('projects-container');
            
            if (projects.length === 0) {
                renderEmptyState();
                return;
            }

            const projectsGrid = document.createElement('div');
            projectsGrid.className = 'projects-grid';

            // 添加创建项目卡片
            const createCard = document.createElement('div');
            createCard.className = 'project-card create-project-card';
            createCard.onclick = showCreateModal;
            createCard.innerHTML = `
                <i class="fas fa-plus"></i>
                <h3>创建新项目</h3>
                <p>开始一个新的机场导航规划项目</p>
            `;
            projectsGrid.appendChild(createCard);

            // 添加现有项目卡片
            projects.forEach(project => {
                const card = createProjectCard(project);
                projectsGrid.appendChild(card);
            });

            container.innerHTML = '';
            container.appendChild(projectsGrid);
        }

        // 创建项目卡片
        function createProjectCard(project) {
            const card = document.createElement('div');
            card.className = 'project-card';
            card.onclick = () => openProject(project.id);

            const createdDate = new Date(project.created_at).toLocaleDateString('zh-CN');
            const updatedDate = new Date(project.updated_at).toLocaleDateString('zh-CN');

            card.innerHTML = `
                <div class="project-header">
                    <div class="project-title">${project.name}</div>
                </div>
                <div class="project-description">${project.description || '暂无描述'}</div>
                <div class="project-meta">
                    <div class="meta-item">
                        <i class="fas fa-calendar-plus"></i>
                        <span>创建于 ${createdDate}</span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-clock"></i>
                        <span>更新于 ${updatedDate}</span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>${project.airport_coords.lat.toFixed(6)}, ${project.airport_coords.lng.toFixed(6)}</span>
                    </div>
                </div>
                <div class="project-actions">
                    <button class="btn btn-small btn-edit" onclick="event.stopPropagation(); openProject('${project.id}')">
                        <i class="fas fa-edit"></i>
                        编辑
                    </button>
                    <button class="btn btn-small btn-delete" onclick="event.stopPropagation(); deleteProject('${project.id}')">
                        <i class="fas fa-trash"></i>
                        删除
                    </button>
                </div>
            `;

            return card;
        }

        // 渲染空状态
        function renderEmptyState() {
            const container = document.getElementById('projects-container');
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-folder-open"></i>
                    <h2>还没有项目</h2>
                    <p>创建您的第一个机场导航规划项目开始使用</p>
                    <button class="btn btn-primary" onclick="showCreateModal()">
                        <i class="fas fa-plus"></i>
                        创建第一个项目
                    </button>
                </div>
            `;
        }

        // 显示创建项目模态框
        function showCreateModal() {
            document.getElementById('createModal').style.display = 'block';
        }

        // 隐藏创建项目模态框
        function hideCreateModal() {
            document.getElementById('createModal').style.display = 'none';
            document.getElementById('createProjectForm').reset();
        }

        // 处理创建项目表单提交
        document.getElementById('createProjectForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const projectData = {
                name: formData.get('name'),
                description: formData.get('description'),
                airport_coords: {
                    lat: parseFloat(formData.get('lat')),
                    lng: parseFloat(formData.get('lng'))
                }
            };

            try {
                const response = await fetch('/api/projects', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(projectData)
                });

                if (response.ok) {
                    const result = await response.json();
                    hideCreateModal();
                    loadProjects(); // 重新加载项目列表
                } else {
                    alert('创建项目失败');
                }
            } catch (error) {
                console.error('创建项目出错:', error);
                alert('创建项目出错');
            }
        });

        // 打开项目
        function openProject(projectId) {
            window.location.href = `/project/${projectId}`;
        }

        // 删除项目
        async function deleteProject(projectId) {
            if (!confirm('确定要删除这个项目吗？此操作不可恢复。')) {
                return;
            }

            try {
                const response = await fetch(`/api/projects/${projectId}`, {
                    method: 'DELETE'
                });

                if (response.ok) {
                    loadProjects(); // 重新加载项目列表
                } else {
                    alert('删除项目失败');
                }
            } catch (error) {
                console.error('删除项目出错:', error);
                alert('删除项目出错');
            }
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('createModal');
            if (event.target === modal) {
                hideCreateModal();
            }
        }
    </script>
</body>
</html>
