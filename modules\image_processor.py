"""
图片处理模块
负责图片的上传、处理、属性调整（透明度、缩放、旋转）
"""

import os
import uuid
from datetime import datetime
from PIL import Image, ImageEnhance
import numpy as np
from typing import Dict, Optional, Tuple, List
import json

# 增加PIL的图片大小限制，防止解压缩炸弹错误
Image.MAX_IMAGE_PIXELS = None  # 移除像素限制，或者设置为更大的值如 500000000

class ImageProcessor:
    def __init__(self, upload_folder: str):
        self.upload_folder = upload_folder
        os.makedirs(upload_folder, exist_ok=True)
    
    def process_upload(self, file, project_id: str) -> Dict:
        """处理上传的图片"""
        try:
            # 验证文件类型
            allowed_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.gif'}
            file_ext = os.path.splitext(file.filename)[1].lower()
            if file_ext not in allowed_extensions:
                raise ValueError(f"不支持的文件格式: {file_ext}")

            # 生成唯一文件名
            image_id = str(uuid.uuid4())
            safe_filename = "".join(c for c in file.filename if c.isalnum() or c in '._-')
            filename = f"{project_id}_{image_id}_{safe_filename}"
            filepath = os.path.join(self.upload_folder, filename)

            # 保存原始文件
            file.save(filepath)

            # 检查文件大小
            file_size = os.path.getsize(filepath)
            max_size = 50 * 1024 * 1024  # 50MB限制
            if file_size > max_size:
                os.remove(filepath)  # 删除过大的文件
                raise ValueError(f"文件过大: {file_size / (1024*1024):.1f}MB，最大允许50MB")

            # 获取图片信息
            temp_filepath = None
            try:
                # 先获取基本信息
                with Image.open(filepath) as img:
                    width, height = img.size
                    format_type = img.format

                    # 检查图片尺寸
                    max_pixels = 50000000  # 5000万像素限制
                    if width * height > max_pixels:
                        raise ValueError(f"图片像素过多: {width}x{height}={width*height:,}，最大允许{max_pixels:,}像素")

                    # 如果图片过大，需要缩放
                    needs_resize = width > 4096 or height > 4096

                    if needs_resize:
                        # 计算缩放比例
                        scale = min(4096/width, 4096/height)
                        new_width = int(width * scale)
                        new_height = int(height * scale)

                        # 创建缩放版本
                        img_resized = img.resize((new_width, new_height), Image.Resampling.LANCZOS)

                        # 保存缩放版本到临时文件
                        temp_filename = f"temp_{filename}"
                        temp_filepath = os.path.join(self.upload_folder, temp_filename)
                        img_resized.save(temp_filepath, format=format_type, quality=90)

                        # 更新尺寸信息
                        width, height = new_width, new_height

                # 如果需要缩放，替换原文件
                if needs_resize:
                    # 删除原文件
                    if os.path.exists(filepath):
                        os.remove(filepath)
                    # 重命名临时文件
                    os.rename(temp_filepath, filepath)
                    temp_filepath = None  # 已经重命名，不需要清理
                    file_size = os.path.getsize(filepath)
                else:
                    file_size = os.path.getsize(filepath)

            except Exception as e:
                # 清理文件
                for cleanup_path in [filepath, temp_filepath]:
                    if cleanup_path and os.path.exists(cleanup_path):
                        try:
                            os.remove(cleanup_path)
                        except:
                            pass
                raise ValueError(f"图片处理失败: {str(e)}")

            # 创建图片数据记录
            image_data = {
                'id': image_id,
                'filename': filename,
                'original_name': file.filename,
                'filepath': filepath,
                'width': width,
                'height': height,
                'format': format_type,
                'file_size': file_size,
                'properties': {
                    'opacity': 0.6,
                    'scale': 1.0,
                    'rotation': 0.0,
                    'position': {'x': 0, 'y': 0},
                    'visible': True,
                    'locked': False
                },
                'transform': {
                    'base_zoom_level': 16,
                    'base_scale': 1.0,
                    'map_scale': 1.0
                },
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat()
            }

            return image_data

        except Exception as e:
            print(f"图片处理错误: {e}")
            raise
    
    def update_image_properties(self, project_id: str, image_id: str, 
                               properties: Dict) -> Optional[Dict]:
        """更新图片属性"""
        # 这里应该从项目数据中获取图片信息
        # 为了简化，我们假设图片数据已经存在
        
        # 验证属性值
        valid_properties = {}
        
        if 'opacity' in properties:
            opacity = float(properties['opacity'])
            valid_properties['opacity'] = max(0.0, min(1.0, opacity))
        
        if 'scale_x' in properties:
            scale_x = float(properties['scale_x'])
            valid_properties['scale_x'] = max(0.1, min(5.0, scale_x))
        
        if 'scale_y' in properties:
            scale_y = float(properties['scale_y'])
            valid_properties['scale_y'] = max(0.1, min(5.0, scale_y))
        
        if 'rotation' in properties:
            rotation = float(properties['rotation'])
            valid_properties['rotation'] = rotation % 360
        
        if 'position' in properties:
            position = properties['position']
            if isinstance(position, dict) and 'x' in position and 'y' in position:
                valid_properties['position'] = {
                    'x': float(position['x']),
                    'y': float(position['y'])
                }
        
        # 返回更新后的属性
        return {
            'id': image_id,
            'properties': valid_properties
        }
    
    def apply_transformations(self, image_path: str, properties: Dict) -> str:
        """应用图片变换并返回新文件路径"""
        with Image.open(image_path) as img:
            # 转换为RGBA模式以支持透明度
            if img.mode != 'RGBA':
                img = img.convert('RGBA')
            
            # 应用透明度
            if 'opacity' in properties:
                opacity = properties['opacity']
                # 创建透明度遮罩
                alpha = img.split()[-1]
                alpha = alpha.point(lambda p: int(p * opacity))
                img.putalpha(alpha)
            
            # 应用缩放
            if 'scale_x' in properties or 'scale_y' in properties:
                scale_x = properties.get('scale_x', 1.0)
                scale_y = properties.get('scale_y', 1.0)
                new_size = (int(img.width * scale_x), int(img.height * scale_y))
                img = img.resize(new_size, Image.Resampling.LANCZOS)
            
            # 应用旋转
            if 'rotation' in properties and properties['rotation'] != 0:
                rotation = properties['rotation']
                img = img.rotate(rotation, expand=True)
            
            # 保存处理后的图片
            processed_filename = f"processed_{uuid.uuid4()}.png"
            processed_path = os.path.join(self.upload_folder, processed_filename)
            img.save(processed_path, 'PNG')
            
            return processed_path
    
    def get_image_overlay_data(self, image_data: Dict) -> Dict:
        """获取图片叠加数据，用于在地图上显示"""
        return {
            'id': image_data['id'],
            'url': f"/uploads/{image_data['filename']}",
            'bounds': self._calculate_bounds(image_data),
            'properties': image_data['properties']
        }
    
    def _calculate_bounds(self, image_data: Dict) -> Dict:
        """计算图片在地图上的边界"""
        properties = image_data['properties']
        position = properties.get('position', {'x': 0, 'y': 0})
        scale_x = properties.get('scale_x', 1.0)
        scale_y = properties.get('scale_y', 1.0)
        
        # 这里需要根据实际的地图坐标系统来计算边界
        # 简化实现，返回相对坐标
        width = image_data['width'] * scale_x
        height = image_data['height'] * scale_y
        
        return {
            'north': position['y'] + height / 2,
            'south': position['y'] - height / 2,
            'east': position['x'] + width / 2,
            'west': position['x'] - width / 2
        }
    
    def delete_image(self, image_id: str) -> bool:
        """删除图片文件"""
        # 这里需要从项目数据中获取图片路径
        # 简化实现
        try:
            # 查找并删除文件
            for filename in os.listdir(self.upload_folder):
                if filename.startswith(image_id):
                    filepath = os.path.join(self.upload_folder, filename)
                    os.remove(filepath)
                    return True
            return False
        except OSError:
            return False


