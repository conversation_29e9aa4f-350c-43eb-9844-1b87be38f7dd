"""
图片处理模块
负责图片的上传、处理、属性调整（透明度、缩放、旋转）
"""

import os
import uuid
from datetime import datetime
from PIL import Image, ImageEnhance
import numpy as np
from typing import Dict, Optional, Tuple, List
import json

class ImageProcessor:
    def __init__(self, upload_folder: str):
        self.upload_folder = upload_folder
        os.makedirs(upload_folder, exist_ok=True)
    
    def process_upload(self, file, project_id: str) -> Dict:
        """处理上传的图片"""
        # 生成唯一文件名
        image_id = str(uuid.uuid4())
        filename = f"{project_id}_{image_id}_{file.filename}"
        filepath = os.path.join(self.upload_folder, filename)

        # 保存原始文件
        file.save(filepath)

        # 获取图片信息
        with Image.open(filepath) as img:
            width, height = img.size
            format_type = img.format

        # 创建图片数据记录
        image_data = {
            'id': image_id,
            'filename': filename,
            'original_name': file.filename,
            'filepath': filepath,
            'width': width,
            'height': height,
            'format': format_type,
            'file_size': os.path.getsize(filepath),
            'properties': {
                'opacity': 0.6,
                'scale': 1.0,
                'rotation': 0.0,
                'position': {'x': 0, 'y': 0},
                'visible': True,
                'locked': False
            },
            'transform': {
                'base_zoom_level': 16,
                'base_scale': 1.0,
                'map_scale': 1.0
            },
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }

        return image_data
    
    def update_image_properties(self, project_id: str, image_id: str, 
                               properties: Dict) -> Optional[Dict]:
        """更新图片属性"""
        # 这里应该从项目数据中获取图片信息
        # 为了简化，我们假设图片数据已经存在
        
        # 验证属性值
        valid_properties = {}
        
        if 'opacity' in properties:
            opacity = float(properties['opacity'])
            valid_properties['opacity'] = max(0.0, min(1.0, opacity))
        
        if 'scale_x' in properties:
            scale_x = float(properties['scale_x'])
            valid_properties['scale_x'] = max(0.1, min(5.0, scale_x))
        
        if 'scale_y' in properties:
            scale_y = float(properties['scale_y'])
            valid_properties['scale_y'] = max(0.1, min(5.0, scale_y))
        
        if 'rotation' in properties:
            rotation = float(properties['rotation'])
            valid_properties['rotation'] = rotation % 360
        
        if 'position' in properties:
            position = properties['position']
            if isinstance(position, dict) and 'x' in position and 'y' in position:
                valid_properties['position'] = {
                    'x': float(position['x']),
                    'y': float(position['y'])
                }
        
        # 返回更新后的属性
        return {
            'id': image_id,
            'properties': valid_properties
        }
    
    def apply_transformations(self, image_path: str, properties: Dict) -> str:
        """应用图片变换并返回新文件路径"""
        with Image.open(image_path) as img:
            # 转换为RGBA模式以支持透明度
            if img.mode != 'RGBA':
                img = img.convert('RGBA')
            
            # 应用透明度
            if 'opacity' in properties:
                opacity = properties['opacity']
                # 创建透明度遮罩
                alpha = img.split()[-1]
                alpha = alpha.point(lambda p: int(p * opacity))
                img.putalpha(alpha)
            
            # 应用缩放
            if 'scale_x' in properties or 'scale_y' in properties:
                scale_x = properties.get('scale_x', 1.0)
                scale_y = properties.get('scale_y', 1.0)
                new_size = (int(img.width * scale_x), int(img.height * scale_y))
                img = img.resize(new_size, Image.Resampling.LANCZOS)
            
            # 应用旋转
            if 'rotation' in properties and properties['rotation'] != 0:
                rotation = properties['rotation']
                img = img.rotate(rotation, expand=True)
            
            # 保存处理后的图片
            processed_filename = f"processed_{uuid.uuid4()}.png"
            processed_path = os.path.join(self.upload_folder, processed_filename)
            img.save(processed_path, 'PNG')
            
            return processed_path
    
    def get_image_overlay_data(self, image_data: Dict) -> Dict:
        """获取图片叠加数据，用于在地图上显示"""
        return {
            'id': image_data['id'],
            'url': f"/uploads/{image_data['filename']}",
            'bounds': self._calculate_bounds(image_data),
            'properties': image_data['properties']
        }
    
    def _calculate_bounds(self, image_data: Dict) -> Dict:
        """计算图片在地图上的边界"""
        properties = image_data['properties']
        position = properties.get('position', {'x': 0, 'y': 0})
        scale_x = properties.get('scale_x', 1.0)
        scale_y = properties.get('scale_y', 1.0)
        
        # 这里需要根据实际的地图坐标系统来计算边界
        # 简化实现，返回相对坐标
        width = image_data['width'] * scale_x
        height = image_data['height'] * scale_y
        
        return {
            'north': position['y'] + height / 2,
            'south': position['y'] - height / 2,
            'east': position['x'] + width / 2,
            'west': position['x'] - width / 2
        }
    
    def delete_image(self, image_id: str) -> bool:
        """删除图片文件"""
        # 这里需要从项目数据中获取图片路径
        # 简化实现
        try:
            # 查找并删除文件
            for filename in os.listdir(self.upload_folder):
                if filename.startswith(image_id):
                    filepath = os.path.join(self.upload_folder, filename)
                    os.remove(filepath)
                    return True
            return False
        except OSError:
            return False


