#!/usr/bin/env python3
import requests

response = requests.get('http://localhost:5000/api/projects/e8a31bb8-3584-429f-82b7-feab15ff2583')
if response.ok:
    project = response.json()
    print('项目名称:', project['name'])
    print('图片数量:', len(project.get('images', [])))
    for i, img in enumerate(project.get('images', [])):
        print(f'  图片 {i+1}: {img["original_name"]} ({img["width"]}x{img["height"]})')
else:
    print('错误:', response.status_code, '-', response.text)
