<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<title>Nav<PERSON><PERSON></title>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/leaflet@1.9.4/dist/leaflet.css"/>
<script src="https://cdn.jsdelivr.net/npm/leaflet@1.9.4/dist/leaflet.js"></script>
<!-- 移除 DistortableImage 插件依赖，使用自定义旋转实现 -->
<style>
  body,html{height:100%;margin:0;padding:0;}
  .layout{display:flex;height:100%;}
  .sidebar{width:320px;box-sizing:border-box;border-right:1px solid #ddd;padding:10px;overflow:auto;background:#fafafa}
  .section{margin-bottom:14px;padding-bottom:10px;border-bottom:1px dashed #ddd}
  .section h3{margin:6px 0 10px;font-size:16px}
  #map{flex:1; position:relative}
  .overlay-screen{position:absolute; inset:0; z-index: 400; pointer-events:none}
  .overlay-screen img{position:absolute; left:0; top:0; transform-origin:center center; pointer-events:auto}
  
  /* 自定义图片旋转控制样式 */
  .custom-image-overlay {
    position: absolute;
    z-index: 500;
    pointer-events: auto;
    cursor: move;
  }
  
  .custom-image-overlay img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    pointer-events: none;
  }
  
  .image-controls {
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 20px;
    background: #007cba;
    border: 2px solid white;
    border-radius: 50%;
    cursor: pointer;
    z-index: 501;
  }
  
  .image-controls::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 8px;
    height: 8px;
    background: white;
    border-radius: 50%;
  }
  
  .corner-handle {
    position: absolute;
    width: 12px;
    height: 12px;
    background: #007cba;
    border: 2px solid white;
    border-radius: 50%;
    cursor: pointer;
    z-index: 501;
  }
  
  .corner-handle.nw { top: -6px; left: -6px; }
  .corner-handle.ne { top: -6px; right: -6px; }
  .corner-handle.sw { bottom: -6px; left: -6px; }
  .corner-handle.se { bottom: -6px; right: -6px; }
  .control-row{margin:6px 0}
  .range-row{display:flex;align-items:center;gap:8px}
  .range-row input[type="range"]{flex:1}
  .btn{margin-right:6px;margin-bottom:6px}
  /* 旧的 Leaflet ImageOverlay 样式已不再使用 */
</style>
</head>
<body>

<div class="layout">
  <div class="sidebar">
    <div class="section">
      <h3>区域绘制</h3>
      <div class="control-row">
        <button class="btn" onclick="startDraw()">绘制区域</button>
        <button class="btn" onclick="finishPolygon()">闭合</button>
      </div>
      <div class="control-row">
        <label>NavMesh三角最大面积(°²)</label>
        <input type="number" id="meshArea" value="0.00001" step="0.000005" style="width:120px">
        <button class="btn" onclick="genNavMesh()">生成NavMesh</button>
      </div>
    </div>

    <div class="section">
      <h3>路径规划</h3>
      <div class="control-row">
        <button class="btn" onclick="setStart()">起点</button>
        <button class="btn" onclick="setGoal()">终点</button>
      </div>
      <div class="control-row">
        <button class="btn" onclick="calcPath()">寻路</button>
        <button class="btn" onclick="optimizePath()">优化路线</button>
      </div>
    </div>

    <div class="section">
      <h3>底图上传与叠加</h3>
      <div class="control-row">
        <input type="file" id="imageFile" accept="image/*">
        <button class="btn" onclick="uploadBasemap()">上传底图</button>
        <button class="btn" onclick="deleteBasemap()" id="delBtn" disabled>删除底图</button>
      </div>
      <div class="control-row range-row">
        <label style="width:60px">透明</label>
        <input type="range" id="imgOpacity" min="0" max="1" step="0.01" value="0.6" oninput="updateOverlayOpacityPreview(this.value)" onchange="updateOverlayOpacity(this.value)">
        <span id="opacityVal">0.6</span>
        <input type="number" id="opacityNum" min="0" max="1" step="0.01" value="0.6" style="width:72px"/>
        <button class="btn" onclick="applyOpacityFromInput()">应用</button>
      </div>
      <div class="control-row range-row">
        <label style="width:60px">缩放</label>
        <input type="range" id="imgScale" min="0.1" max="10" step="0.01" value="1" oninput="document.getElementById('scaleVal').innerText=Number(this.value).toFixed(2)" onchange="applyScaleFromSlider(this.value)">
        <span id="scaleVal">1.00</span>
        <input type="number" id="scaleNum" min="0.1" max="10" step="0.01" value="1.00" style="width:72px"/>
        <button class="btn" onclick="applyScaleFromInput()">应用</button>
      </div>
      <div class="control-row range-row">
        <label style="width:60px">旋转</label>
        <input type="range" id="imgRotate" min="-180" max="180" step="1" value="0" oninput="document.getElementById('rotateVal').innerText=this.value+'°'" onchange="applyRotationFromSlider(this.value)">
        <span id="rotateVal">0°</span>
        <input type="number" id="rotateNum" min="-180" max="180" step="1" value="0" style="width:72px"/>
        <button class="btn" onclick="applyRotationFromInput()">应用</button>
      </div>
      <div class="control-row">
        <button class="btn" onclick="resetOverlay()">重置</button>
        <button class="btn" onclick="toggleOverlayDrag()" id="dragBtn">开启拖动图片</button>
      </div>
      <div style="font-size:12px;color:#666">
        说明：点击底图可选中；拖动图片移动；拖动四角句柄缩放；拖动上方蓝色句柄旋转；透明度、缩放、旋转可通过滑块调节。
      </div>
    </div>
  </div>
  <div id="map"></div>
</div>

<script>
var map=L.map('map').setView([30.314,104.445],16);
L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}').addTo(map);

var drawing=false, polyPoints=[], polygonLayer=null;
var meshLayer=L.layerGroup().addTo(map);
var pathLayer=L.layerGroup().addTo(map);
var startMarker=null, goalMarker=null, setMode=null;

// 自定义图片叠加状态
var customOverlay = null; // 自定义图片叠加层
var overlayDraggingEnabled = false;
var dragging = false;
var overlayAbsScale = 1.0; // 绝对缩放
var overlayAbsRotate = 0.0; // 绝对旋转角（度）
var overlayOpacity = 0.6; // 透明度
var currentImageUrl = null;
var isRotating = false;
var isResizing = false;
var dragStartPos = null;
var initialTransform = null;

// 地图缩放相关状态
var mapZoomLevel = 16; // 当前地图缩放级别
var baseZoomLevel = 16; // 图片创建时的基准缩放级别
var imageMapScale = 1.0; // 图片相对于地图的缩放比例

map.on('zoomstart', function(){
  // 防止缩放中拖动叠图，避免抖动
  dragging=false;
});

// 监听地图缩放事件，同步调整图片大小
map.on('zoom', function(e){
  if (customOverlay) {
    updateImageScaleForMapZoom();
  }
});

// 监听地图缩放结束事件
map.on('zoomend', function(e){
  mapZoomLevel = map.getZoom();
});

function startDraw(){drawing=true; polyPoints=[]; if(polygonLayer) map.removeLayer(polygonLayer);} 
map.on('click', function(e){
  if(drawing){
    polyPoints.push([e.latlng.lat,e.latlng.lng]);
    if(polygonLayer) map.removeLayer(polygonLayer);
    polygonLayer=L.polyline(polyPoints,{color:'yellow'}).addTo(map);
  }
  if(setMode=="start"){
    if(startMarker) map.removeLayer(startMarker);
    startMarker=L.marker(e.latlng).addTo(map).bindPopup("起点").openPopup();
    setMode=null;
  }
  if(setMode=="goal"){
    if(goalMarker) map.removeLayer(goalMarker);
    goalMarker=L.marker(e.latlng).addTo(map).bindPopup("终点").openPopup();
    setMode=null;
  }
});

function finishPolygon(){
  if(polyPoints.length<3){ alert("至少三个点"); return;}
  drawing=false;
  if(polygonLayer) map.removeLayer(polygonLayer);
  polygonLayer=L.polygon(polyPoints,{color:'red',fillColor:'red',fillOpacity:0.3}).addTo(map);
}

function genNavMesh(){
  const maxArea=parseFloat(document.getElementById("meshArea").value);
  fetch('/navmesh',{
    method:'POST', headers:{'Content-Type':'application/json'},
    body:JSON.stringify({polygon:polyPoints,max_area:maxArea})
  }).then(r=>r.json()).then(data=>{
    meshLayer.clearLayers();
    data.tris.forEach(tri=>{ L.polygon(tri,{color:'cyan',weight:1,fillOpacity:0.1}).addTo(meshLayer); });
    alert("生成三角形: "+data.tris.length);
  });
}

function setStart(){ setMode="start"; }
function setGoal(){ setMode="goal"; }

function calcPath(){
  if(!startMarker || !goalMarker){ alert("请放置起点和终点"); return;}
  fetch('/path',{
    method:'POST', headers:{'Content-Type':'application/json'},
    body:JSON.stringify({
      polygon: polyPoints,
      start:[startMarker.getLatLng().lat,startMarker.getLatLng().lng],
      goal:[goalMarker.getLatLng().lat,goalMarker.getLatLng().lng]
    })
  }).then(r=>r.json()).then(data=>{
    pathLayer.clearLayers();
    if(data.path.length==0){ alert("无可行路径"); return; }
    L.polyline(data.path,{color:'lime',weight:4}).addTo(pathLayer);
  });
}

function optimizePath(){
  if(!startMarker || !goalMarker){ alert("请放置起点和终点"); return;}
  fetch('/optimize_path',{
    method:'POST', headers:{'Content-Type':'application/json'},
    body:JSON.stringify({
      polygon: polyPoints,
      start:[startMarker.getLatLng().lat,startMarker.getLatLng().lng],
      goal:[goalMarker.getLatLng().lat,goalMarker.getLatLng().lng]
    })
  }).then(r=>r.json()).then(data=>{
    pathLayer.clearLayers();
    if(data.path.length==0){ alert("无可行路径"); return; }
    L.polyline(data.path,{color:'orange',weight:4}).addTo(pathLayer);
  });
}

// ---------- 图片预览压缩（降低交互时的渲染负担） ----------
async function createPreview(file, maxDim=1536){
  try{
    const blob = file instanceof Blob ? file : new Blob([file]);
    const bmp = await createImageBitmap(blob);
    const scale = Math.min(1, maxDim / Math.max(bmp.width, bmp.height));
    const w = Math.max(1, Math.round(bmp.width * scale));
    const h = Math.max(1, Math.round(bmp.height * scale));
    const canvas = ('OffscreenCanvas' in window) ? new OffscreenCanvas(w, h) : document.createElement('canvas');
    if(!(canvas instanceof OffscreenCanvas)) { canvas.width = w; canvas.height = h; }
    const ctx = canvas.getContext('2d', { alpha: true });
    ctx.imageSmoothingQuality = 'high';
    ctx.drawImage(bmp, 0, 0, w, h);
    const outBlob = (canvas instanceof OffscreenCanvas)
      ? await canvas.convertToBlob({ type: 'image/webp', quality: 0.9 })
      : await new Promise(res=> canvas.toBlob(res, 'image/webp', 0.9));
    return URL.createObjectURL(outBlob);
  }catch(e){ return null; }
}

// ---------- 上传并显示（预览 -> 真图替换） ----------
async function uploadBasemap(){
  const fileInput=document.getElementById('imageFile');
  if(!fileInput.files || fileInput.files.length===0){ alert('请选择图片'); return; }
  const file = fileInput.files[0];
  // 先生成预览，立即显示
  const previewUrl = await createPreview(file, 2048);
  if(previewUrl){ addOverlayOnMap(previewUrl); }
  // 然后上传原图，完成后用真图替换，保留当前角点
  const formData=new FormData();
  formData.append('image', file);
  fetch('/upload_image', { method:'POST', body: formData })
    .then(async r=>{
      const txt = await r.text();
      let data = {}; try { data = JSON.parse(txt); } catch(e){ throw new Error('服务端返回非JSON: '+txt); }
      if(!r.ok || data.error){ throw new Error(data && (data.message || data.error) || '上传失败'); }
      replaceOverlayImagePreserveCorners(data.url);
    })
    .catch(err=>{ alert('上传失败: '+ (err && err.message ? err.message : err)); });
}

// 创建自定义图片叠加层
function createCustomImageOverlay(imgUrl) {
  // 移除现有的叠加层
  if (customOverlay) {
    customOverlay.remove();
  }
  
  // 记录当前地图缩放级别作为基准
  baseZoomLevel = map.getZoom();
  mapZoomLevel = baseZoomLevel;
  
  // 创建图片容器
  const container = document.createElement('div');
  container.className = 'custom-image-overlay';
  container.style.position = 'absolute';
  container.style.zIndex = '500';
  
  // 创建图片元素
  const img = document.createElement('img');
  img.src = imgUrl;
  img.style.width = '100%';
  img.style.height = '100%';
  img.style.objectFit = 'contain';
  img.style.pointerEvents = 'none';
  
  // 创建旋转控制手柄
  const rotateHandle = document.createElement('div');
  rotateHandle.className = 'image-controls';
  rotateHandle.style.cursor = 'pointer';
  
  // 创建四个角控制手柄
  const handles = ['nw', 'ne', 'sw', 'se'].map(pos => {
    const handle = document.createElement('div');
    handle.className = `corner-handle ${pos}`;
    handle.style.cursor = 'pointer';
    return handle;
  });
  
  // 组装容器
  container.appendChild(img);
  container.appendChild(rotateHandle);
  handles.forEach(handle => container.appendChild(handle));
  
  // 设置初始位置和大小
  const mapSize = map.getSize();
  const centerX = mapSize.x / 2;
  const centerY = mapSize.y / 2;
  const width = Math.min(mapSize.x * 0.6, 400);
  const height = Math.min(mapSize.y * 0.5, 300);
  
  container.style.left = (centerX - width / 2) + 'px';
  container.style.top = (centerY - height / 2) + 'px';
  container.style.width = width + 'px';
  container.style.height = height + 'px';
  
  // 存储初始尺寸用于缩放计算
  container.dataset.initialWidth = width;
  container.dataset.initialHeight = height;
  container.dataset.initialLeft = (centerX - width / 2);
  container.dataset.initialTop = (centerY - height / 2);
  
  // 添加到地图容器
  map.getContainer().appendChild(container);
  
  // 绑定事件
  bindCustomOverlayEvents(container, rotateHandle, handles);
  
  customOverlay = container;
  currentImageUrl = imgUrl;
  
  // 更新UI状态
  document.getElementById('delBtn').disabled = false;
  enableRotationUI(true);
  
  return container;
}

function addOverlayOnMap(img){
  overlayAbsScale = 1.0; 
  overlayAbsRotate = 0.0;
  overlayOpacity = parseFloat(document.getElementById('imgOpacity').value) || 0.6;
  
  const imgUrl = (typeof img === 'string') ? img : (img.url || img);
  
  // 使用自定义图片叠加层
  createCustomImageOverlay(imgUrl);
}

function replaceOverlayImagePreserveCorners(newUrl){
  if(!customOverlay) return addOverlayOnMap(newUrl);
  
  // 保持当前的位置、大小和变换状态，只替换图片
  const img = customOverlay.querySelector('img');
  if (img) {
    img.src = newUrl;
    currentImageUrl = newUrl;
  }
}

function enableRotationUI(enabled){
  // 始终允许编辑（如不支持在应用时提示）
  const rotRange = document.getElementById('imgRotate');
  const rotNum = document.getElementById('rotateNum');
  if(rotRange) rotRange.disabled = false;
  if(rotNum) rotNum.disabled = false;
}

// 绑定自定义叠加层事件
function bindCustomOverlayEvents(container, rotateHandle, handles) {
  const img = container.querySelector('img');
  
  // 容器拖拽事件
  container.addEventListener('mousedown', (e) => {
    if (overlayDraggingEnabled && !isRotating && !isResizing) {
      dragging = true;
      dragStartPos = { x: e.clientX, y: e.clientY };
      initialTransform = {
        left: parseInt(container.style.left),
        top: parseInt(container.style.top)
      };
      container.style.cursor = 'grabbing';
      e.preventDefault();
    }
  });
  
  // 旋转手柄事件
  rotateHandle.addEventListener('mousedown', (e) => {
    isRotating = true;
    dragStartPos = { x: e.clientX, y: e.clientY };
    initialTransform = {
      rotation: overlayAbsRotate,
      left: parseInt(container.style.left),
      top: parseInt(container.style.top),
      width: parseInt(container.style.width),
      height: parseInt(container.style.height)
    };
    e.preventDefault();
    e.stopPropagation();
  });
  
  // 角控制手柄事件
  handles.forEach((handle, index) => {
    handle.addEventListener('mousedown', (e) => {
      isResizing = true;
      dragStartPos = { x: e.clientX, y: e.clientY };
      initialTransform = {
        left: parseInt(container.style.left),
        top: parseInt(container.style.top),
        width: parseInt(container.style.width),
        height: parseInt(container.style.height)
      };
      e.preventDefault();
      e.stopPropagation();
    });
  });
  
  // 全局鼠标移动事件
  document.addEventListener('mousemove', (e) => {
    if (dragging && !isRotating && !isResizing) {
      const deltaX = e.clientX - dragStartPos.x;
      const deltaY = e.clientY - dragStartPos.y;
      
      container.style.left = (initialTransform.left + deltaX) + 'px';
      container.style.top = (initialTransform.top + deltaY) + 'px';
    } else if (isRotating) {
      const centerX = initialTransform.left + initialTransform.width / 2;
      const centerY = initialTransform.top + initialTransform.height / 2;
      const angle = Math.atan2(e.clientY - centerY, e.clientX - centerX) * 180 / Math.PI;
      
      overlayAbsRotate = angle;
      applyCustomTransform();
      
      // 更新UI
      document.getElementById('imgRotate').value = Math.round(overlayAbsRotate);
      document.getElementById('rotateVal').innerText = Math.round(overlayAbsRotate) + '°';
      document.getElementById('rotateNum').value = Math.round(overlayAbsRotate);
    } else if (isResizing) {
      const deltaX = e.clientX - dragStartPos.x;
      const deltaY = e.clientY - dragStartPos.y;
      
      const newWidth = Math.max(50, initialTransform.width + deltaX);
      const newHeight = Math.max(50, initialTransform.height + deltaY);
      
      container.style.width = newWidth + 'px';
      container.style.height = newHeight + 'px';
      
      // 更新缩放值（考虑地图缩放的影响）
      const baseWidth = parseFloat(container.dataset.initialWidth);
      const scale = newWidth / baseWidth;
      overlayAbsScale = scale;
      document.getElementById('imgScale').value = scale.toFixed(2);
      document.getElementById('scaleVal').innerText = scale.toFixed(2);
      document.getElementById('scaleNum').value = scale.toFixed(2);
    }
  });
  
  // 全局鼠标释放事件
  document.addEventListener('mouseup', () => {
    if (dragging || isRotating || isResizing) {
      dragging = false;
      isRotating = false;
      isResizing = false;
      container.style.cursor = 'move';
    }
  });
}

// 应用自定义变换
function applyCustomTransform() {
  if (!customOverlay) return;
  
  const img = customOverlay.querySelector('img');
  if (img) {
    img.style.transform = `rotate(${overlayAbsRotate}deg) scale(${overlayAbsScale})`;
    img.style.opacity = overlayOpacity;
  }
}

// 根据地图缩放更新图片大小和位置
function updateImageScaleForMapZoom() {
  if (!customOverlay) return;
  
  const currentZoom = map.getZoom();
  const zoomDiff = currentZoom - baseZoomLevel;
  
  // 计算缩放比例：每级缩放对应2倍的变化
  const scaleFactor = Math.pow(2, zoomDiff);
  
  // 获取初始尺寸
  const initialWidth = parseFloat(customOverlay.dataset.initialWidth);
  const initialHeight = parseFloat(customOverlay.dataset.initialHeight);
  const initialLeft = parseFloat(customOverlay.dataset.initialLeft);
  const initialTop = parseFloat(customOverlay.dataset.initialTop);
  
  // 计算新的尺寸和位置
  const newWidth = initialWidth * scaleFactor;
  const newHeight = initialHeight * scaleFactor;
  
  // 计算新的位置（保持中心点不变）
  const mapSize = map.getSize();
  const centerX = mapSize.x / 2;
  const centerY = mapSize.y / 2;
  const newLeft = centerX - newWidth / 2;
  const newTop = centerY - newHeight / 2;
  
  // 应用新的尺寸和位置
  customOverlay.style.width = newWidth + 'px';
  customOverlay.style.height = newHeight + 'px';
  customOverlay.style.left = newLeft + 'px';
  customOverlay.style.top = newTop + 'px';
  
  // 更新图片相对于地图的缩放比例
  imageMapScale = scaleFactor;
}

// 透明度：预览与应用
function updateOverlayOpacityPreview(val){ document.getElementById('opacityVal').innerText=(+val).toFixed(2); }
function updateOverlayOpacity(val){
  document.getElementById('opacityVal').innerText=parseFloat(val).toFixed(2);
  const num = document.getElementById('opacityNum'); if (num) num.value = (+val).toFixed(2);
  overlayOpacity = parseFloat(val);
  applyCustomTransform();
}
function applyOpacityFromInput(){
  const num = document.getElementById('opacityNum'); if(!num) return;
  const v = Math.min(1, Math.max(0, parseFloat(num.value)||0));
  document.getElementById('imgOpacity').value = v.toFixed(2);
  updateOverlayOpacity(v);
}

function applyScaleFromSlider(val){
  const target = Math.min(10, Math.max(0.1, parseFloat(val)||1));
  overlayAbsScale = target;
  applyCustomTransform();
  const num = document.getElementById('scaleNum'); if(num) num.value = target.toFixed(2);
  document.getElementById('scaleVal').innerText = target.toFixed(2);
}
function applyScaleFromInput(){
  const el = document.getElementById('scaleNum'); if(!el) return;
  const target = Math.min(10, Math.max(0.1, parseFloat(el.value)||1));
  overlayAbsScale = target;
  applyCustomTransform();
  document.getElementById('imgScale').value = target.toFixed(2);
  document.getElementById('scaleVal').innerText = target.toFixed(2);
}
function applyRotationFromSlider(val){
  let target = parseFloat(val)||0;
  target = ((target+180)%360+360)%360 - 180;
  overlayAbsRotate = target;
  applyCustomTransform();
  const num = document.getElementById('rotateNum'); if(num) num.value = String(Math.round(target));
  document.getElementById('rotateVal').innerText = Math.round(target)+"°";
}
function applyRotationFromInput(){
  const el = document.getElementById('rotateNum'); if(!el) return;
  let target = parseFloat(el.value)||0;
  target = ((target+180)%360+360)%360 - 180;
  overlayAbsRotate = target;
  applyCustomTransform();
  document.getElementById('imgRotate').value = String(Math.round(target));
  document.getElementById('rotateVal').innerText = Math.round(target)+"°";
}

// 移除不再需要的 DistortableImage 相关函数

function toggleOverlayDrag(){
  overlayDraggingEnabled = !overlayDraggingEnabled;
  const btn=document.getElementById('dragBtn');
  if(btn) btn.textContent = overlayDraggingEnabled ? '关闭拖动图片' : '开启拖动图片';
  
  if (customOverlay) {
    customOverlay.style.cursor = overlayDraggingEnabled ? 'move' : 'default';
  }
}

// 移除不再需要的 DistortableImage 相关函数

function resetOverlay(){
  if(!customOverlay) return;
  overlayAbsScale = 1.0; 
  overlayAbsRotate = 0;
  overlayOpacity = 0.6;
  
  // 重置到初始尺寸和位置
  const initialWidth = parseFloat(customOverlay.dataset.initialWidth);
  const initialHeight = parseFloat(customOverlay.dataset.initialHeight);
  const initialLeft = parseFloat(customOverlay.dataset.initialLeft);
  const initialTop = parseFloat(customOverlay.dataset.initialTop);
  
  customOverlay.style.width = initialWidth + 'px';
  customOverlay.style.height = initialHeight + 'px';
  customOverlay.style.left = initialLeft + 'px';
  customOverlay.style.top = initialTop + 'px';
  
  // 重置基准缩放级别
  baseZoomLevel = map.getZoom();
  imageMapScale = 1.0;
  
  document.getElementById('imgScale').value='1';
  document.getElementById('imgRotate').value='0';
  document.getElementById('imgOpacity').value='0.6';
  document.getElementById('scaleVal').innerText='1.00';
  document.getElementById('rotateVal').innerText='0°';
  document.getElementById('opacityVal').innerText='0.60';
  document.getElementById('scaleNum').value='1.00';
  document.getElementById('rotateNum').value='0';
  document.getElementById('opacityNum').value='0.60';
  
  applyCustomTransform();
}

async function deleteBasemap(){
  if(!customOverlay){ return; }
  try{
    if(currentImageUrl){
      // 这里可以添加删除服务器端文件的逻辑
      // await fetch('/delete_image', {
      //   method:'POST', headers:{'Content-Type':'application/json'},
      //   body: JSON.stringify({filename: currentImageUrl})
      // });
    }
  }catch(e){}
  
  try { 
    customOverlay.remove(); 
  }catch(e){}
  
  customOverlay = null;
  currentImageUrl = null;
  document.getElementById('delBtn').disabled = true;
}

// 移除不再需要的函数
</script>
</body>
</html>
