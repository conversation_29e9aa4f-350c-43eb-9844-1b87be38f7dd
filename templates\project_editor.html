<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目编辑器 - 机场导航规划平台</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/leaflet@1.9.4/dist/leaflet.css"/>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/leaflet@1.9.4/dist/leaflet.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            height: 100vh;
            overflow: hidden;
            background: #f5f7fa;
        }

        .app-layout {
            display: flex;
            height: 100vh;
        }

        /* 顶部工具栏 */
        .top-toolbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid #e0e6ed;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 1rem;
            z-index: 1000;
        }

        .toolbar-left {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .back-btn {
            background: none;
            border: none;
            font-size: 1.2rem;
            color: #6c757d;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .back-btn:hover {
            background: #f8f9fa;
            color: #495057;
        }

        .project-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2c3e50;
        }

        .toolbar-center {
            display: flex;
            gap: 0.5rem;
        }

        .tool-btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            background: white;
            color: #6c757d;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .tool-btn:hover {
            background: #f8f9fa;
            color: #495057;
        }

        .tool-btn.active {
            background: #007bff;
            color: white;
        }

        .toolbar-right {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }

        .save-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.2s ease;
        }

        .save-btn:hover {
            background: #218838;
        }

        /* 左侧面板 */
        .left-panel {
            width: 320px;
            background: white;
            border-right: 1px solid #e0e6ed;
            display: flex;
            flex-direction: column;
            margin-top: 60px;
            height: calc(100vh - 60px);
        }

        .panel-tabs {
            display: flex;
            border-bottom: 1px solid #e0e6ed;
        }

        .panel-tab {
            flex: 1;
            padding: 1rem;
            background: none;
            border: none;
            cursor: pointer;
            font-weight: 600;
            color: #6c757d;
            transition: all 0.2s ease;
        }

        .panel-tab.active {
            color: #007bff;
            border-bottom: 2px solid #007bff;
        }

        .panel-content {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
        }

        .section {
            margin-bottom: 2rem;
        }

        .section-title {
            font-size: 1rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .control-group {
            margin-bottom: 1rem;
        }

        .control-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #495057;
            font-size: 0.9rem;
        }

        .control-input {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 0.9rem;
        }

        .control-input:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        .range-control {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .range-control input[type="range"] {
            flex: 1;
        }

        .range-value {
            min-width: 60px;
            text-align: center;
            font-size: 0.9rem;
            color: #6c757d;
        }

        .btn-group {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: 1px solid #ced4da;
            border-radius: 4px;
            background: white;
            color: #495057;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.2s ease;
        }

        .btn:hover {
            background: #f8f9fa;
        }

        .btn-primary {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }

        .btn-primary:hover {
            background: #0056b3;
        }

        .btn-success {
            background: #28a745;
            color: white;
            border-color: #28a745;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
            border-color: #dc3545;
        }

        /* 地图容器 */
        .map-container {
            flex: 1;
            position: relative;
            margin-top: 60px;
        }

        #map {
            width: 100%;
            height: 100%;
        }

        /* 自定义图片叠加样式 */
        .custom-image-overlay {
            position: absolute;
            z-index: 500;
            pointer-events: auto;
            cursor: move;
        }

        .custom-image-overlay img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            pointer-events: none;
        }

        .image-controls {
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 20px;
            background: #007bff;
            border: 2px solid white;
            border-radius: 50%;
            cursor: pointer;
            z-index: 501;
        }

        .corner-handle {
            position: absolute;
            width: 12px;
            height: 12px;
            background: #007bff;
            border: 2px solid white;
            border-radius: 50%;
            cursor: pointer;
            z-index: 501;
        }

        .corner-handle.nw { top: -6px; left: -6px; }
        .corner-handle.ne { top: -6px; right: -6px; }
        .corner-handle.sw { bottom: -6px; left: -6px; }
        .corner-handle.se { bottom: -6px; right: -6px; }

        /* 状态指示器 */
        .status-indicator {
            position: fixed;
            bottom: 20px;
            right: 20px;
            padding: 0.5rem 1rem;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            border-radius: 20px;
            font-size: 0.9rem;
            z-index: 1000;
            display: none;
        }

        .status-indicator.show {
            display: block;
        }

        .status-indicator.success {
            background: rgba(40, 167, 69, 0.9);
        }

        .status-indicator.error {
            background: rgba(220, 53, 69, 0.9);
        }

        .status-indicator.info {
            background: rgba(23, 162, 184, 0.9);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .left-panel {
                width: 280px;
            }
            
            .toolbar-center {
                display: none;
            }
        }

        @media (max-width: 640px) {
            .left-panel {
                position: fixed;
                left: -320px;
                z-index: 999;
                transition: left 0.3s ease;
            }
            
            .left-panel.open {
                left: 0;
            }
            
            .map-container {
                margin-left: 0;
            }
        }

        /* 图片列表样式 */
        .image-list {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .image-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem;
            border: 1px solid #e0e6ed;
            border-radius: 4px;
            background: #f8f9fa;
        }

        .image-thumbnail {
            width: 40px;
            height: 40px;
            object-fit: cover;
            border-radius: 4px;
        }

        .image-info {
            flex: 1;
            font-size: 0.9rem;
        }

        .image-name {
            font-weight: 500;
            color: #2c3e50;
        }

        .image-size {
            color: #6c757d;
            font-size: 0.8rem;
        }

        .image-actions {
            display: flex;
            gap: 0.25rem;
        }

        .icon-btn {
            width: 28px;
            height: 28px;
            border: none;
            background: none;
            cursor: pointer;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            transition: all 0.2s ease;
        }

        .icon-btn:hover {
            background: #e9ecef;
            color: #495057;
        }

        .icon-btn.danger:hover {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="app-layout">
        <!-- 顶部工具栏 -->
        <div class="top-toolbar">
            <div class="toolbar-left">
                <button class="back-btn" onclick="goBack()">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <div class="project-title" id="projectTitle">加载中...</div>
            </div>
            
            <div class="toolbar-center">
                <button class="tool-btn" id="drawTool" onclick="setTool('draw')">
                    <i class="fas fa-draw-polygon"></i>
                    绘制区域
                </button>
                <button class="tool-btn" id="pathTool" onclick="setTool('path')">
                    <i class="fas fa-route"></i>
                    路径规划
                </button>
                <button class="tool-btn" id="measureTool" onclick="setTool('measure')">
                    <i class="fas fa-ruler"></i>
                    测量
                </button>
            </div>
            
            <div class="toolbar-right">
                <button class="save-btn" onclick="saveProject()">
                    <i class="fas fa-save"></i>
                    保存
                </button>
            </div>
        </div>

        <!-- 左侧面板 -->
        <div class="left-panel">
            <div class="panel-tabs">
                <button class="panel-tab active" onclick="switchTab('layers')">图层</button>
                <button class="panel-tab" onclick="switchTab('tools')">工具</button>
                <button class="panel-tab" onclick="switchTab('settings')">设置</button>
            </div>
            
            <div class="panel-content">
                <!-- 图层面板 -->
                <div id="layersPanel" class="tab-panel">
                    <div class="section">
                        <div class="section-title">
                            <i class="fas fa-images"></i>
                            底图管理
                        </div>
                        <div class="control-group">
                            <input type="file" id="imageFile" accept="image/*" style="display: none;">
                            <button class="btn btn-primary" onclick="document.getElementById('imageFile').click()">
                                <i class="fas fa-upload"></i>
                                上传底图
                            </button>
                        </div>
                        <div id="imageList" class="image-list">
                            <!-- 图片列表将在这里动态生成 -->
                        </div>
                    </div>

                    <div class="section">
                        <div class="section-title">
                            <i class="fas fa-layer-group"></i>
                            图层控制
                        </div>
                        <div class="control-group">
                            <label class="control-label">透明度</label>
                            <div class="range-control">
                                <input type="range" id="opacitySlider" min="0" max="1" step="0.01" value="0.6">
                                <span class="range-value" id="opacityValue">0.6</span>
                            </div>
                        </div>
                        <div class="control-group">
                            <label class="control-label">缩放</label>
                            <div class="range-control">
                                <input type="range" id="scaleSlider" min="0.1" max="5" step="0.01" value="1">
                                <span class="range-value" id="scaleValue">1.0</span>
                            </div>
                        </div>
                        <div class="control-group">
                            <label class="control-label">旋转</label>
                            <div class="range-control">
                                <input type="range" id="rotationSlider" min="-180" max="180" step="1" value="0">
                                <span class="range-value" id="rotationValue">0°</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 工具面板 -->
                <div id="toolsPanel" class="tab-panel" style="display: none;">
                    <div class="section">
                        <div class="section-title">
                            <i class="fas fa-draw-polygon"></i>
                            区域绘制
                        </div>
                        <div class="btn-group">
                            <button class="btn" onclick="startDrawing()">开始绘制</button>
                            <button class="btn" onclick="finishPolygon()">完成多边形</button>
                            <button class="btn btn-danger" onclick="clearDrawing()">清除</button>
                        </div>
                        <div class="control-group">
                            <label class="control-label">网格大小 (米)</label>
                            <input type="number" class="control-input" id="gridSize" value="50" min="10" max="200">
                        </div>
                        <div class="btn-group">
                            <button class="btn btn-success" onclick="generateNavMesh()">生成导航网格</button>
                        </div>
                    </div>

                    <div class="section">
                        <div class="section-title">
                            <i class="fas fa-route"></i>
                            路径规划
                        </div>
                        <div class="btn-group">
                            <button class="btn" onclick="setStartPoint()">设置起点</button>
                            <button class="btn" onclick="setEndPoint()">设置终点</button>
                        </div>
                        <div class="btn-group">
                            <button class="btn btn-success" onclick="calculatePath()">计算路径</button>
                            <button class="btn" onclick="optimizePath()">优化路径</button>
                        </div>
                    </div>
                </div>

                <!-- 设置面板 -->
                <div id="settingsPanel" class="tab-panel" style="display: none;">
                    <div class="section">
                        <div class="section-title">
                            <i class="fas fa-cog"></i>
                            项目设置
                        </div>
                        <div class="control-group">
                            <label class="control-label">项目名称</label>
                            <input type="text" class="control-input" id="projectNameInput">
                        </div>
                        <div class="control-group">
                            <label class="control-label">项目描述</label>
                            <textarea class="control-input" id="projectDescInput" rows="3"></textarea>
                        </div>
                        <div class="control-group">
                            <label class="control-label">机场坐标</label>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.5rem;">
                                <input type="number" class="control-input" id="airportLat" placeholder="纬度" step="0.000001">
                                <input type="number" class="control-input" id="airportLng" placeholder="经度" step="0.000001">
                            </div>
                        </div>
                    </div>

                    <div class="section">
                        <div class="section-title">
                            <i class="fas fa-map"></i>
                            地图设置
                        </div>
                        <div class="control-group">
                            <label class="control-label">默认缩放级别</label>
                            <input type="number" class="control-input" id="defaultZoom" min="1" max="20" value="16">
                        </div>
                        <div class="control-group">
                            <label class="control-label">网格精度</label>
                            <input type="number" class="control-input" id="meshArea" step="0.000001" value="0.00001">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 地图容器 -->
        <div class="map-container">
            <div id="map"></div>
        </div>
    </div>

    <!-- 状态指示器 -->
    <div id="statusIndicator" class="status-indicator"></div>

    <script>
        // 全局变量
        let map;
        let currentProject = null;
        let projectId = null;
        let currentTool = null;
        let drawing = false;
        let polyPoints = [];
        let polygonLayer = null;
        let meshLayer = null;
        let pathLayer = null;
        let startMarker = null;
        let goalMarker = null;
        // 图片叠加相关变量
        let customOverlay = null;
        let currentImageData = null;
        let overlayDraggingEnabled = true;
        let overlayOpacity = 0.6;
        let overlayScale = 1.0;
        let overlayRotation = 0.0;
        let dragging = false;
        let isRotating = false;
        let isResizing = false;
        let dragStartPos = null;
        let initialTransform = null;

        // 地理坐标系统 - 实现图片与地图的完全一致性
        let imageGeoBounds = null; // 图片的地理边界 {north, south, east, west}
        let imageGeoCenter = null; // 图片的地理中心点 {lat, lng}
        let imagePixelSize = null; // 图片在当前缩放级别下的像素尺寸
        let baseZoomLevel = 16;    // 图片设置时的基准缩放级别
        let imageMetersPerPixel = null; // 每像素对应的米数

        // 滚轮操作模式控制
        let wheelMode = 'map'; // 'map' 或 'image'
        let isMouseOverImage = false; // 鼠标是否在图片上
        let ctrlKeyPressed = false; // Ctrl键是否按下

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 从URL获取项目ID
            const pathParts = window.location.pathname.split('/');
            projectId = pathParts[pathParts.length - 1];
            
            initializeMap();
            loadProject();
            bindEvents();
        });

        // 初始化地图
        function initializeMap() {
            map = L.map('map').setView([30.314, 104.445], 16);
            L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}').addTo(map);

            // 创建图层组
            meshLayer = L.layerGroup().addTo(map);
            pathLayer = L.layerGroup().addTo(map);

            // 绑定地图事件
            map.on('click', onMapClick);
            map.on('zoomstart', onZoomStart);
            map.on('zoom', onMapZoom);
            map.on('zoomend', onZoomEnd);

            // 记录初始缩放级别
            baseZoomLevel = map.getZoom();
            currentZoomLevel = baseZoomLevel;
        }

        // 地图缩放开始事件
        function onZoomStart() {
            // 防止缩放中拖动叠图，避免抖动
            dragging = false;
        }

        // 地图缩放事件
        function onMapZoom() {
            if (customOverlay) {
                updateImageScaleForMapZoom();
            }
        }

        // 地图缩放结束事件
        function onZoomEnd() {
            currentZoomLevel = map.getZoom();
        }

        // 根据地图缩放更新图片大小和位置 - 实现完全的地理一致性
        function updateImageScaleForMapZoom() {
            if (!customOverlay || !imageGeoBounds || !imageGeoCenter) return;

            const currentZoom = map.getZoom();

            // 计算图片在当前缩放级别下应该的像素尺寸
            const bounds = L.latLngBounds(
                [imageGeoBounds.south, imageGeoBounds.west],
                [imageGeoBounds.north, imageGeoBounds.east]
            );

            // 获取地理边界在屏幕上的像素坐标
            const northWest = map.latLngToContainerPoint([imageGeoBounds.north, imageGeoBounds.west]);
            const southEast = map.latLngToContainerPoint([imageGeoBounds.south, imageGeoBounds.east]);

            // 计算图片应该的像素尺寸和位置
            const pixelWidth = Math.abs(southEast.x - northWest.x);
            const pixelHeight = Math.abs(southEast.y - northWest.y);
            const pixelLeft = Math.min(northWest.x, southEast.x);
            const pixelTop = Math.min(northWest.y, southEast.y);

            // 应用计算出的尺寸和位置
            customOverlay.style.width = pixelWidth + 'px';
            customOverlay.style.height = pixelHeight + 'px';
            customOverlay.style.left = pixelLeft + 'px';
            customOverlay.style.top = pixelTop + 'px';

            // 更新存储的像素尺寸
            imagePixelSize = { width: pixelWidth, height: pixelHeight };

            console.log(`地图缩放更新: zoom=${currentZoom}, size=${pixelWidth}x${pixelHeight}, pos=(${pixelLeft},${pixelTop})`);
        }

        // 加载项目数据
        async function loadProject() {
            try {
                console.log('加载项目:', projectId);
                const response = await fetch(`/api/projects/${projectId}`);
                console.log('项目加载响应状态:', response.status);

                if (response.ok) {
                    currentProject = await response.json();
                    console.log('项目数据加载成功:', currentProject);

                    // 确保images数组存在
                    if (!currentProject.images) {
                        currentProject.images = [];
                    }

                    updateUI();
                    showStatus('项目加载成功', 'success');
                } else {
                    const errorText = await response.text();
                    console.error('项目加载失败:', response.status, errorText);
                    showStatus(`项目加载失败: ${response.status}`, 'error');
                }
            } catch (error) {
                console.error('加载项目出错:', error);
                showStatus('项目加载出错: ' + error.message, 'error');
            }
        }

        // 更新UI
        function updateUI() {
            if (!currentProject) return;
            
            // 更新项目标题
            document.getElementById('projectTitle').textContent = currentProject.name;
            
            // 更新设置面板
            document.getElementById('projectNameInput').value = currentProject.name;
            document.getElementById('projectDescInput').value = currentProject.description || '';
            document.getElementById('airportLat').value = currentProject.airport_coords.lat;
            document.getElementById('airportLng').value = currentProject.airport_coords.lng;
            
            // 更新地图中心
            map.setView([currentProject.airport_coords.lat, currentProject.airport_coords.lng], 
                       currentProject.settings?.map_zoom || 16);
            
            // 加载图片
            updateImageList();
            
            // 加载区域
            loadRegions();
        }

        // 更新图片列表
        function updateImageList() {
            const imageList = document.getElementById('imageList');
            if (!imageList) {
                console.error('imageList element not found');
                return;
            }

            imageList.innerHTML = '';

            if (currentProject && currentProject.images && currentProject.images.length > 0) {
                console.log('更新图片列表，图片数量:', currentProject.images.length);
                currentProject.images.forEach((image, index) => {
                    try {
                        const imageItem = createImageItem(image);
                        imageList.appendChild(imageItem);
                    } catch (error) {
                        console.error(`创建图片项失败 (索引 ${index}):`, error, image);
                    }
                });
            } else {
                console.log('没有图片需要显示');
            }
        }

        // 创建图片项
        function createImageItem(image) {
            if (!image || !image.id || !image.filename) {
                console.error('无效的图片数据:', image);
                return document.createElement('div');
            }

            const item = document.createElement('div');
            item.className = 'image-item';

            // 安全地获取图片属性
            const filename = image.filename || 'unknown';
            const originalName = image.original_name || 'Unknown';
            const width = image.width || 0;
            const height = image.height || 0;
            const imageId = image.id;

            item.innerHTML = `
                <img src="/uploads/${filename}" alt="${originalName}" class="image-thumbnail"
                     onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjZjBmMGYwIi8+CjxwYXRoIGQ9Ik0yMCAyNUMyMi43NjE0IDI1IDI1IDIyLjc2MTQgMjUgMjBDMjUgMTcuMjM4NiAyMi43NjE0IDE1IDIwIDE1QzE3LjIzODYgMTUgMTUgMTcuMjM4NiAxNSAyMEMxNSAyMi43NjE0IDE3LjIzODYgMjUgMjAgMjVaIiBmaWxsPSIjY2NjIi8+Cjwvc3ZnPgo='">
                <div class="image-info">
                    <div class="image-name">${originalName}</div>
                    <div class="image-size">${width} × ${height}</div>
                </div>
                <div class="image-actions">
                    <button class="icon-btn" onclick="showImage('${imageId}')" title="显示">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="icon-btn danger" onclick="removeImage('${imageId}')" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;
            return item;
        }

        // 绑定事件
        function bindEvents() {
            // 文件上传
            document.getElementById('imageFile').addEventListener('change', handleImageUpload);
            
            // 滑块事件
            document.getElementById('opacitySlider').addEventListener('input', updateOpacity);
            document.getElementById('scaleSlider').addEventListener('input', updateScale);
            document.getElementById('rotationSlider').addEventListener('input', updateRotation);
        }

        // 处理图片上传
        async function handleImageUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            // 验证文件类型
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/bmp', 'image/tiff'];
            if (!allowedTypes.includes(file.type)) {
                showStatus('不支持的图片格式', 'error');
                event.target.value = '';
                return;
            }

            // 验证文件大小 (16MB)
            if (file.size > 16 * 1024 * 1024) {
                showStatus('图片文件过大，请选择小于16MB的图片', 'error');
                event.target.value = '';
                return;
            }

            const formData = new FormData();
            formData.append('image', file);

            try {
                showStatus('上传中...', 'info');
                console.log('开始上传图片:', file.name, 'to project:', projectId);

                const response = await fetch(`/api/projects/${projectId}/images`, {
                    method: 'POST',
                    body: formData
                });

                console.log('上传响应状态:', response.status);

                if (response.ok) {
                    const imageData = await response.json();
                    console.log('上传成功，图片数据:', imageData);

                    // 确保currentProject存在并且有images数组
                    if (!currentProject) {
                        console.error('currentProject is null');
                        showStatus('项目数据错误', 'error');
                        return;
                    }

                    if (!currentProject.images) {
                        currentProject.images = [];
                    }

                    currentProject.images.push(imageData);
                    updateImageList();
                    showStatus('图片上传成功', 'success');
                } else {
                    const errorText = await response.text();
                    console.error('上传失败:', response.status, errorText);
                    showStatus(`图片上传失败: ${response.status}`, 'error');
                }
            } catch (error) {
                console.error('上传图片出错:', error);
                showStatus('上传图片出错: ' + error.message, 'error');
            }

            // 清空文件输入
            event.target.value = '';
        }

        // 显示状态消息
        function showStatus(message, type = 'info') {
            const indicator = document.getElementById('statusIndicator');
            indicator.textContent = message;
            indicator.className = `status-indicator show ${type}`;
            
            setTimeout(() => {
                indicator.classList.remove('show');
            }, 3000);
        }

        // 切换面板标签
        function switchTab(tabName) {
            // 更新标签状态
            document.querySelectorAll('.panel-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // 显示对应面板
            document.querySelectorAll('.tab-panel').forEach(panel => {
                panel.style.display = 'none';
            });
            document.getElementById(tabName + 'Panel').style.display = 'block';
        }

        // 设置工具
        function setTool(toolName) {
            // 更新工具按钮状态
            document.querySelectorAll('.tool-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.getElementById(toolName + 'Tool').classList.add('active');
            
            currentTool = toolName;
        }

        // 地图点击事件
        function onMapClick(e) {
            if (currentTool === 'draw' && drawing) {
                polyPoints.push([e.latlng.lat, e.latlng.lng]);
                if (polygonLayer) map.removeLayer(polygonLayer);
                polygonLayer = L.polyline(polyPoints, {color: 'yellow'}).addTo(map);
            }
        }

        // 开始绘制
        function startDrawing() {
            drawing = true;
            polyPoints = [];
            if (polygonLayer) map.removeLayer(polygonLayer);
            setTool('draw');
        }

        // 完成多边形
        function finishPolygon() {
            if (polyPoints.length < 3) {
                alert('至少需要三个点');
                return;
            }
            drawing = false;
            if (polygonLayer) map.removeLayer(polygonLayer);
            polygonLayer = L.polygon(polyPoints, {color: 'red', fillColor: 'red', fillOpacity: 0.3}).addTo(map);
        }

        // 清除绘制
        function clearDrawing() {
            drawing = false;
            polyPoints = [];
            if (polygonLayer) map.removeLayer(polygonLayer);
            meshLayer.clearLayers();
            pathLayer.clearLayers();
        }

        // 生成导航网格
        async function generateNavMesh() {
            if (polyPoints.length < 3) {
                alert('请先绘制区域');
                return;
            }
            
            const gridSize = parseInt(document.getElementById('gridSize').value);
            
            try {
                showStatus('生成网格中...', 'info');
                const response = await fetch(`/api/projects/${projectId}/grid`, {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({
                        grid_size: gridSize
                    })
                });
                
                if (response.ok) {
                    const gridData = await response.json();
                    // 在地图上显示网格
                    displayGrid(gridData);
                    showStatus('网格生成成功', 'success');
                } else {
                    showStatus('网格生成失败', 'error');
                }
            } catch (error) {
                console.error('生成网格出错:', error);
                showStatus('生成网格出错', 'error');
            }
        }

        // 显示网格
        function displayGrid(gridData) {
            meshLayer.clearLayers();
            if (gridData.nodes) {
                gridData.nodes.forEach(node => {
                    L.circleMarker([node[0], node[1]], {
                        radius: 2,
                        color: 'cyan',
                        fillOpacity: 0.6
                    }).addTo(meshLayer);
                });
            }
        }

        // 更新透明度
        function updateOpacity(event) {
            overlayOpacity = parseFloat(event.target.value);
            document.getElementById('opacityValue').textContent = overlayOpacity.toFixed(2);
            if (customOverlay) {
                applyImageTransform();
                // 保存属性
                saveImageProperties();
            }
        }

        // 更新缩放
        function updateScale(event) {
            const newScale = parseFloat(event.target.value);
            overlayScale = newScale;

            document.getElementById('scaleValue').textContent = overlayScale.toFixed(2);

            if (customOverlay && imageGeoBounds) {
                // 基于地理坐标重新计算尺寸
                const center = imageGeoCenter;
                const geoWidth = (imageGeoBounds.east - imageGeoBounds.west) * newScale;
                const geoHeight = (imageGeoBounds.north - imageGeoBounds.south) * newScale;

                // 更新地理边界
                imageGeoBounds = {
                    north: center.lat + geoHeight / 2,
                    south: center.lat - geoHeight / 2,
                    east: center.lng + geoWidth / 2,
                    west: center.lng - geoWidth / 2
                };

                // 重新计算像素位置
                updateImageScaleForMapZoom();
                applyImageTransform();

                // 保存属性
                saveImageProperties();
            }
        }

        // 更新旋转
        function updateRotation(event) {
            overlayRotation = parseInt(event.target.value);
            document.getElementById('rotationValue').textContent = overlayRotation + '°';
            if (customOverlay) {
                applyImageTransform();
                // 保存属性
                saveImageProperties();
            }
        }

        // 应用图片变换
        function applyImageTransform() {
            if (!customOverlay) return;

            const img = customOverlay.querySelector('img');
            if (img) {
                img.style.transform = `rotate(${overlayRotation}deg)`;
                img.style.opacity = overlayOpacity;
            }
        }

        // 从像素位置更新地理坐标
        function updateGeoBoundsFromPixelPosition() {
            if (!customOverlay || !imagePixelSize) return;

            const rect = customOverlay.getBoundingClientRect();
            const mapContainer = map.getContainer().getBoundingClientRect();

            // 计算图片在地图容器中的相对位置
            const left = rect.left - mapContainer.left;
            const top = rect.top - mapContainer.top;
            const right = left + rect.width;
            const bottom = top + rect.height;

            // 转换为地理坐标
            const northWest = map.containerPointToLatLng([left, top]);
            const southEast = map.containerPointToLatLng([right, bottom]);

            imageGeoBounds = {
                north: northWest.lat,
                south: southEast.lat,
                east: southEast.lng,
                west: northWest.lng
            };

            imageGeoCenter = {
                lat: (imageGeoBounds.north + imageGeoBounds.south) / 2,
                lng: (imageGeoBounds.east + imageGeoBounds.west) / 2
            };

            // 保存到图片数据
            if (currentImageData) {
                if (!currentImageData.properties) currentImageData.properties = {};
                currentImageData.properties.geo_bounds = imageGeoBounds;
                currentImageData.properties.geo_center = imageGeoCenter;

                // 自动保存
                saveImageProperties();
            }
        }

        // 更新滑块值显示
        function updateSliderValues() {
            document.getElementById('opacitySlider').value = overlayOpacity;
            document.getElementById('opacityValue').textContent = overlayOpacity.toFixed(2);

            document.getElementById('scaleSlider').value = overlayScale;
            document.getElementById('scaleValue').textContent = overlayScale.toFixed(2);

            document.getElementById('rotationSlider').value = overlayRotation;
            document.getElementById('rotationValue').textContent = overlayRotation.toFixed(0) + '°';
        }

        // 保存项目
        async function saveProject() {
            if (!currentProject) return;
            
            // 更新项目数据
            currentProject.name = document.getElementById('projectNameInput').value;
            currentProject.description = document.getElementById('projectDescInput').value;
            currentProject.airport_coords = {
                lat: parseFloat(document.getElementById('airportLat').value),
                lng: parseFloat(document.getElementById('airportLng').value)
            };
            
            try {
                showStatus('保存中...', 'info');
                const response = await fetch(`/api/projects/${projectId}`, {
                    method: 'PUT',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify(currentProject)
                });
                
                if (response.ok) {
                    showStatus('保存成功', 'success');
                } else {
                    showStatus('保存失败', 'error');
                }
            } catch (error) {
                console.error('保存项目出错:', error);
                showStatus('保存出错', 'error');
            }
        }

        // 显示地理定位对话框
        function showGeolocationDialog() {
            const dialog = document.createElement('div');
            dialog.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0,0,0,0.7);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
            `;

            const content = document.createElement('div');
            content.style.cssText = `
                background: white;
                padding: 20px;
                border-radius: 8px;
                max-width: 400px;
                width: 90%;
            `;

            content.innerHTML = `
                <h3>设置图片地理位置</h3>
                <p>请输入图片对应的地理坐标范围：</p>
                <div style="margin: 10px 0;">
                    <label>北纬：</label>
                    <input type="number" id="geoNorth" step="0.000001" value="${imageGeoBounds ? imageGeoBounds.north.toFixed(6) : ''}" style="width: 100px;">
                </div>
                <div style="margin: 10px 0;">
                    <label>南纬：</label>
                    <input type="number" id="geoSouth" step="0.000001" value="${imageGeoBounds ? imageGeoBounds.south.toFixed(6) : ''}" style="width: 100px;">
                </div>
                <div style="margin: 10px 0;">
                    <label>东经：</label>
                    <input type="number" id="geoEast" step="0.000001" value="${imageGeoBounds ? imageGeoBounds.east.toFixed(6) : ''}" style="width: 100px;">
                </div>
                <div style="margin: 10px 0;">
                    <label>西经：</label>
                    <input type="number" id="geoWest" step="0.000001" value="${imageGeoBounds ? imageGeoBounds.west.toFixed(6) : ''}" style="width: 100px;">
                </div>
                <div style="margin-top: 20px; text-align: right;">
                    <button onclick="this.closest('.dialog').remove()" style="margin-right: 10px;">取消</button>
                    <button onclick="applyGeolocation()" style="background: #007cff; color: white; border: none; padding: 8px 16px; border-radius: 4px;">应用</button>
                </div>
            `;

            dialog.className = 'dialog';
            dialog.appendChild(content);
            document.body.appendChild(dialog);

            // 应用地理定位
            window.applyGeolocation = function() {
                const north = parseFloat(document.getElementById('geoNorth').value);
                const south = parseFloat(document.getElementById('geoSouth').value);
                const east = parseFloat(document.getElementById('geoEast').value);
                const west = parseFloat(document.getElementById('geoWest').value);

                if (isNaN(north) || isNaN(south) || isNaN(east) || isNaN(west)) {
                    alert('请输入有效的坐标值');
                    return;
                }

                imageGeoBounds = { north, south, east, west };
                imageGeoCenter = {
                    lat: (north + south) / 2,
                    lng: (east + west) / 2
                };

                // 更新图片位置
                updateImageScaleForMapZoom();

                // 保存设置
                saveImageProperties();

                dialog.remove();
                showStatus('地理位置设置成功', 'success');
            };
        }

        // 保存图片属性
        function saveImageProperties() {
            if (!currentImageData) return;

            const properties = {
                opacity: overlayOpacity,
                scale: overlayScale,
                rotation: overlayRotation,
                geo_bounds: imageGeoBounds,
                geo_center: imageGeoCenter,
                visible: true,
                locked: false
            };

            // 发送到服务器保存
            fetch(`/api/projects/${projectId}/images/${currentImageData.id}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ properties: properties })
            }).then(response => {
                if (response.ok) {
                    console.log('图片属性保存成功');
                } else {
                    console.error('保存失败');
                }
            }).catch(error => {
                console.error('保存出错:', error);
            });
        }

        // 返回项目列表
        function goBack() {
            window.location.href = '/';
        }

        // 加载区域数据
        function loadRegions() {
            // 这里可以加载和显示已保存的区域
        }

        // 显示图片
        function showImage(imageId) {
            const image = currentProject.images.find(img => img.id === imageId);
            if (image) {
                currentImageData = image;
                // 在地图上显示图片
                createImageOverlay(`/uploads/${image.filename}`, image);
            }
        }

        // 创建图片叠加层 - 支持地理坐标定位
        function createImageOverlay(imageUrl, imageData = null) {
            if (customOverlay) {
                customOverlay.remove();
            }

            // 记录当前地图缩放级别作为基准
            baseZoomLevel = map.getZoom();

            const container = document.createElement('div');
            container.className = 'custom-image-overlay';
            container.style.position = 'absolute';
            container.style.zIndex = '500';
            container.style.cursor = 'move';
            container.style.border = '2px solid #007cff';
            container.style.boxShadow = '0 0 10px rgba(0,124,255,0.5)';

            const img = document.createElement('img');
            img.src = imageUrl;
            img.style.width = '100%';
            img.style.height = '100%';
            img.style.objectFit = 'contain';
            img.style.pointerEvents = 'none';
            img.style.borderRadius = '4px';

            // 创建更直观的控制工具栏
            const toolbar = document.createElement('div');
            toolbar.className = 'image-toolbar';
            toolbar.style.cssText = `
                position: absolute;
                top: -40px;
                left: 0;
                right: 0;
                height: 35px;
                background: rgba(0,0,0,0.8);
                border-radius: 4px 4px 0 0;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 10px;
                font-size: 12px;
                color: white;
                z-index: 1002;
            `;

            // 创建旋转控制按钮
            const rotateHandle = document.createElement('button');
            rotateHandle.innerHTML = '<i class="fas fa-redo"></i> 旋转';
            rotateHandle.className = 'control-button';
            rotateHandle.style.cssText = `
                cursor: pointer;
                padding: 5px 8px;
                border: none;
                border-radius: 3px;
                background: rgba(255,255,255,0.2);
                color: white;
                font-size: 11px;
            `;
            rotateHandle.title = '拖拽旋转图片';

            // 创建地理定位按钮
            const geoButton = document.createElement('button');
            geoButton.innerHTML = '<i class="fas fa-map-marker-alt"></i> 定位';
            geoButton.className = 'control-button';
            geoButton.style.cssText = `
                cursor: pointer;
                padding: 5px 8px;
                border: none;
                border-radius: 3px;
                background: rgba(255,255,255,0.2);
                color: white;
                font-size: 11px;
            `;
            geoButton.title = '设置图片地理位置';

            toolbar.appendChild(rotateHandle);
            toolbar.appendChild(geoButton);

            // 创建四个角的缩放控制手柄
            const handles = [
                {pos: 'nw', cursor: 'nw-resize', style: 'top: -5px; left: -5px;'},
                {pos: 'ne', cursor: 'ne-resize', style: 'top: -5px; right: -5px;'},
                {pos: 'sw', cursor: 'sw-resize', style: 'bottom: -5px; left: -5px;'},
                {pos: 'se', cursor: 'se-resize', style: 'bottom: -5px; right: -5px;'}
            ].map(({pos, cursor, style}) => {
                const handle = document.createElement('div');
                handle.className = `resize-handle ${pos}`;
                handle.style.cssText = `
                    position: absolute;
                    width: 10px;
                    height: 10px;
                    background: #007cff;
                    border: 2px solid white;
                    border-radius: 50%;
                    cursor: ${cursor};
                    z-index: 1001;
                    ${style}
                `;
                return handle;
            });

            // 组装容器
            container.appendChild(img);
            container.appendChild(toolbar);
            handles.forEach(handle => container.appendChild(handle));

            // 初始化图片位置和地理坐标
            initializeImagePosition(container, imageData);

            // 添加到地图容器
            map.getContainer().appendChild(container);

            // 绑定事件
            bindImageOverlayEvents(container, rotateHandle, geoButton, handles);

            customOverlay = container;
            applyImageTransform();

            // 更新滑块值
            updateSliderValues();
        }

        // 初始化图片位置和地理坐标
        function initializeImagePosition(container, imageData) {
            const mapSize = map.getSize();
            const mapCenter = map.getCenter();

            if (imageData && imageData.properties && imageData.properties.geo_bounds) {
                // 如果有保存的地理坐标，使用它们
                imageGeoBounds = imageData.properties.geo_bounds;
                imageGeoCenter = {
                    lat: (imageGeoBounds.north + imageGeoBounds.south) / 2,
                    lng: (imageGeoBounds.east + imageGeoBounds.west) / 2
                };

                // 根据地理坐标计算像素位置
                updateImageScaleForMapZoom();
            } else {
                // 默认在地图中心显示，占地图的60%
                const width = Math.min(mapSize.x * 0.6, 500);
                const height = Math.min(mapSize.y * 0.5, 400);
                const centerX = mapSize.x / 2;
                const centerY = mapSize.y / 2;

                container.style.left = (centerX - width / 2) + 'px';
                container.style.top = (centerY - height / 2) + 'px';
                container.style.width = width + 'px';
                container.style.height = height + 'px';

                // 计算默认的地理边界（基于当前地图视图）
                const bounds = map.getBounds();
                const mapWidth = bounds.getEast() - bounds.getWest();
                const mapHeight = bounds.getNorth() - bounds.getSouth();

                // 图片占地图视图的60%
                const geoWidth = mapWidth * 0.6;
                const geoHeight = mapHeight * 0.5;

                imageGeoBounds = {
                    north: mapCenter.lat + geoHeight / 2,
                    south: mapCenter.lat - geoHeight / 2,
                    east: mapCenter.lng + geoWidth / 2,
                    west: mapCenter.lng - geoWidth / 2
                };

                imageGeoCenter = { lat: mapCenter.lat, lng: mapCenter.lng };
                imagePixelSize = { width: width, height: height };
            }

            // 从图片数据恢复其他属性
            if (imageData && imageData.properties) {
                overlayOpacity = imageData.properties.opacity || 0.6;
                overlayScale = imageData.properties.scale || 1.0;
                overlayRotation = imageData.properties.rotation || 0;
            }
        }

        // 绑定图片叠加层事件
        function bindImageOverlayEvents(container, rotateHandle, geoButton, handles) {
            // 容器拖拽事件
            container.addEventListener('mousedown', (e) => {
                if (overlayDraggingEnabled && !isRotating && !isResizing) {
                    dragging = true;
                    dragStartPos = { x: e.clientX, y: e.clientY };
                    initialTransform = {
                        left: parseInt(container.style.left),
                        top: parseInt(container.style.top)
                    };
                    container.style.cursor = 'grabbing';
                    e.preventDefault();
                }
            });

            // 旋转手柄事件
            rotateHandle.addEventListener('mousedown', (e) => {
                isRotating = true;
                dragStartPos = { x: e.clientX, y: e.clientY };
                initialTransform = {
                    rotation: overlayRotation,
                    left: parseInt(container.style.left),
                    top: parseInt(container.style.top),
                    width: parseInt(container.style.width),
                    height: parseInt(container.style.height)
                };
                e.preventDefault();
                e.stopPropagation();
            });

            // 地理定位按钮事件
            geoButton.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                showGeolocationDialog();
            });

            // 角控制手柄事件
            handles.forEach((handle, index) => {
                handle.addEventListener('mousedown', (e) => {
                    isResizing = true;
                    dragStartPos = { x: e.clientX, y: e.clientY };
                    initialTransform = {
                        left: parseInt(container.style.left),
                        top: parseInt(container.style.top),
                        width: parseInt(container.style.width),
                        height: parseInt(container.style.height)
                    };
                    e.preventDefault();
                    e.stopPropagation();
                });
            });

            // 全局鼠标移动事件
            document.addEventListener('mousemove', (e) => {
                if (dragging && !isRotating && !isResizing) {
                    const deltaX = e.clientX - dragStartPos.x;
                    const deltaY = e.clientY - dragStartPos.y;

                    const newLeft = initialTransform.left + deltaX;
                    const newTop = initialTransform.top + deltaY;

                    container.style.left = newLeft + 'px';
                    container.style.top = newTop + 'px';

                    // 同步更新地理坐标
                    updateGeoBoundsFromPixelPosition();
                } else if (isRotating) {
                    const centerX = initialTransform.left + initialTransform.width / 2;
                    const centerY = initialTransform.top + initialTransform.height / 2;
                    const angle = Math.atan2(e.clientY - centerY, e.clientX - centerX) * 180 / Math.PI;

                    overlayRotation = angle;
                    applyImageTransform();

                    // 更新UI
                    document.getElementById('rotationSlider').value = Math.round(overlayRotation);
                    document.getElementById('rotationValue').innerText = Math.round(overlayRotation) + '°';
                } else if (isResizing) {
                    const deltaX = e.clientX - dragStartPos.x;
                    const deltaY = e.clientY - dragStartPos.y;

                    const newWidth = Math.max(50, initialTransform.width + deltaX);
                    const newHeight = Math.max(50, initialTransform.height + deltaY);

                    container.style.width = newWidth + 'px';
                    container.style.height = newHeight + 'px';

                    // 更新缩放值
                    const baseWidth = parseFloat(container.dataset.initialWidth);
                    const scale = newWidth / baseWidth;
                    imageBaseScale = scale;
                    overlayScale = scale;

                    document.getElementById('scaleSlider').value = scale.toFixed(2);
                    document.getElementById('scaleValue').innerText = scale.toFixed(2);
                }
            });

            // 全局鼠标释放事件
            document.addEventListener('mouseup', () => {
                if (dragging || isRotating || isResizing) {
                    dragging = false;
                    isRotating = false;
                    isResizing = false;
                    container.style.cursor = 'move';
                }
            });
        }

        // 移除图片
        async function removeImage(imageId) {
            if (!confirm('确定要删除这张图片吗？')) return;
            
            try {
                // 从项目中移除图片
                currentProject.images = currentProject.images.filter(img => img.id !== imageId);
                updateImageList();
                
                // 如果当前显示的是这张图片，则隐藏
                if (customOverlay) {
                    customOverlay.remove();
                    customOverlay = null;
                }
                
                showStatus('图片删除成功', 'success');
            } catch (error) {
                console.error('删除图片出错:', error);
                showStatus('删除图片出错', 'error');
            }
        }

        // 设置起点
        function setStartPoint() {
            currentTool = 'start';
            map.once('click', function(e) {
                if (startMarker) map.removeLayer(startMarker);
                startMarker = L.marker(e.latlng).addTo(map).bindPopup("起点").openPopup();
            });
        }

        // 设置终点
        function setEndPoint() {
            currentTool = 'end';
            map.once('click', function(e) {
                if (goalMarker) map.removeLayer(goalMarker);
                goalMarker = L.marker(e.latlng).addTo(map).bindPopup("终点").openPopup();
            });
        }

        // 计算路径
        async function calculatePath() {
            if (!startMarker || !goalMarker) {
                alert('请先设置起点和终点');
                return;
            }
            
            try {
                showStatus('计算路径中...', 'info');
                const response = await fetch(`/api/projects/${projectId}/path`, {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({
                        start: [startMarker.getLatLng().lat, startMarker.getLatLng().lng],
                        goal: [goalMarker.getLatLng().lat, goalMarker.getLatLng().lng],
                        algorithm: 'astar'
                    })
                });
                
                if (response.ok) {
                    const pathData = await response.json();
                    if (pathData.path && pathData.path.length > 0) {
                        pathLayer.clearLayers();
                        L.polyline(pathData.path, {color: 'lime', weight: 4}).addTo(pathLayer);
                        showStatus('路径计算成功', 'success');
                    } else {
                        showStatus('未找到可行路径', 'error');
                    }
                } else {
                    showStatus('路径计算失败', 'error');
                }
            } catch (error) {
                console.error('计算路径出错:', error);
                showStatus('计算路径出错', 'error');
            }
        }

        // 优化路径
        function optimizePath() {
            // 路径优化逻辑
            showStatus('路径优化功能开发中...', 'info');
        }
    </script>
</body>
</html>
