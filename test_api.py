#!/usr/bin/env python3
"""
测试API功能
"""

import requests
import json

BASE_URL = "http://localhost:5000"

def test_create_project():
    """测试创建项目"""
    project_data = {
        "name": "测试项目",
        "description": "这是一个测试项目",
        "airport_coords": {
            "lat": 30.314,
            "lng": 104.445
        }
    }
    
    response = requests.post(f"{BASE_URL}/api/projects", json=project_data)
    print(f"创建项目响应: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"项目ID: {result.get('project_id')}")
        return result.get('project_id')
    else:
        print(f"错误: {response.text}")
        return None

def test_get_projects():
    """测试获取项目列表"""
    response = requests.get(f"{BASE_URL}/api/projects")
    print(f"获取项目列表响应: {response.status_code}")
    if response.status_code == 200:
        projects = response.json()
        print(f"项目数量: {len(projects)}")
        for project in projects:
            print(f"- {project['name']} ({project['id']})")
        return projects
    else:
        print(f"错误: {response.text}")
        return []

def test_get_project(project_id):
    """测试获取项目详情"""
    response = requests.get(f"{BASE_URL}/api/projects/{project_id}")
    print(f"获取项目详情响应: {response.status_code}")
    if response.status_code == 200:
        project = response.json()
        print(f"项目名称: {project['name']}")
        print(f"项目描述: {project['description']}")
        print(f"机场坐标: {project['airport_coords']}")
        return project
    else:
        print(f"错误: {response.text}")
        return None

if __name__ == "__main__":
    print("开始测试API...")
    
    # 测试创建项目
    print("\n1. 测试创建项目")
    project_id = test_create_project()
    
    # 测试获取项目列表
    print("\n2. 测试获取项目列表")
    projects = test_get_projects()
    
    # 测试获取项目详情
    if project_id:
        print(f"\n3. 测试获取项目详情 (ID: {project_id})")
        project = test_get_project(project_id)
    
    print("\nAPI测试完成!")
