# 图片与卫星地图重合功能优化

## 概述
全面优化了图片与卫星地图的重合功能，实现了真正的地理坐标一致性和更便于操作的用户界面。

## 主要优化内容

### 1. 地理坐标系统 🌍
实现了基于地理坐标的图片定位系统，确保图片与地图的完全一致性。

#### 核心特性
- **地理边界存储**：每张图片都有精确的地理坐标边界（北纬、南纬、东经、西经）
- **地理中心点**：自动计算并存储图片的地理中心坐标
- **坐标同步**：图片移动时自动更新地理坐标，地图缩放时图片自动调整

#### 技术实现
```javascript
// 地理坐标变量
let imageGeoBounds = null; // {north, south, east, west}
let imageGeoCenter = null; // {lat, lng}
let imagePixelSize = null; // 当前像素尺寸

// 地理坐标与像素坐标转换
function updateImageScaleForMapZoom() {
    const northWest = map.latLngToContainerPoint([imageGeoBounds.north, imageGeoBounds.west]);
    const southEast = map.latLngToContainerPoint([imageGeoBounds.south, imageGeoBounds.east]);
    // 根据地理坐标计算像素位置和尺寸
}
```

### 2. 操作界面优化 🎛️
重新设计了图片控制界面，提供更直观和便捷的操作体验。

#### 新增功能
- **工具栏设计**：图片上方显示半透明工具栏，包含所有控制按钮
- **旋转按钮**：点击式旋转控制，支持拖拽旋转
- **地理定位按钮**：一键打开地理坐标设置对话框
- **可视化手柄**：四个角落的蓝色圆形缩放手柄，更易识别和操作

#### 界面元素
```javascript
// 工具栏样式
toolbar.style.cssText = `
    position: absolute;
    top: -40px;
    background: rgba(0,0,0,0.8);
    border-radius: 4px 4px 0 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
`;

// 缩放手柄样式
handle.style.cssText = `
    width: 10px;
    height: 10px;
    background: #007cff;
    border: 2px solid white;
    border-radius: 50%;
    cursor: ${cursor};
`;
```

### 3. 地图一致性 🗺️
实现了图片与地图的完全同步，解决了缩放和位置不一致的问题。

#### 同步机制
- **缩放同步**：地图缩放时，图片根据地理坐标自动调整大小和位置
- **位置同步**：图片移动时，自动更新地理坐标并保存
- **实时更新**：所有操作都会实时同步到地理坐标系统

#### 核心算法
```javascript
// 地图缩放时的图片更新
function updateImageScaleForMapZoom() {
    // 获取地理边界在屏幕上的像素坐标
    const northWest = map.latLngToContainerPoint([imageGeoBounds.north, imageGeoBounds.west]);
    const southEast = map.latLngToContainerPoint([imageGeoBounds.south, imageGeoBounds.east]);
    
    // 计算并应用新的像素尺寸和位置
    const pixelWidth = Math.abs(southEast.x - northWest.x);
    const pixelHeight = Math.abs(southEast.y - northWest.y);
    
    customOverlay.style.width = pixelWidth + 'px';
    customOverlay.style.height = pixelHeight + 'px';
}
```

### 4. 地理定位功能 📍
新增了精确的地理定位设置功能，支持手动输入坐标。

#### 功能特点
- **坐标输入对话框**：支持输入北纬、南纬、东经、西经坐标
- **精度支持**：支持6位小数精度的坐标输入
- **实时应用**：设置后立即更新图片位置
- **自动保存**：坐标设置自动保存到项目数据

#### 使用场景
```javascript
// 天府机场示例坐标
imageGeoBounds = {
    north: 30.3200,  // 北纬30.32度
    south: 30.3080,  // 南纬30.308度
    east: 104.4520,  // 东经104.452度
    west: 104.4380   // 西经104.438度
};
```

### 5. 属性持久化 💾
实现了图片属性的自动保存和恢复功能。

#### 保存内容
- **地理坐标**：图片的精确地理边界和中心点
- **视觉属性**：透明度、缩放比例、旋转角度
- **状态信息**：可见性、锁定状态等

#### 自动保存机制
```javascript
function saveImageProperties() {
    const properties = {
        opacity: overlayOpacity,
        scale: overlayScale,
        rotation: overlayRotation,
        geo_bounds: imageGeoBounds,
        geo_center: imageGeoCenter,
        visible: true,
        locked: false
    };
    
    // 发送到服务器保存
    fetch(`/api/projects/${projectId}/images/${currentImageData.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ properties: properties })
    });
}
```

## 使用指南

### 基本操作流程
1. **上传图片**：选择并上传机场或其他地理区域的图片
2. **初始定位**：图片默认显示在地图中心，占视图的60%
3. **精确定位**：点击"定位"按钮，输入精确的地理坐标
4. **调整属性**：使用滑块调整透明度、缩放、旋转
5. **拖拽移动**：直接拖拽图片到目标位置
6. **缩放调整**：拖拽四角手柄调整图片大小

### 天府机场示例
```javascript
// 天府机场T1航站楼区域坐标示例
北纬：30.320000
南纬：30.308000  
东经：104.452000
西经：104.438000
```

### 操作技巧
- **地图缩放**：缩放地图时图片会自动保持地理位置不变
- **精确定位**：使用地理定位功能可以将图片精确覆盖到实际地理位置
- **实时保存**：所有操作都会自动保存，刷新页面后设置不会丢失
- **多图片支持**：每张图片都有独立的地理坐标和属性设置

## 技术优势

### 1. 真正的地理一致性
- 图片位置基于地理坐标，不受页面缩放影响
- 地图缩放时图片自动调整，保持地理位置准确

### 2. 直观的操作体验
- 可视化控制手柄，操作更直观
- 工具栏集中显示，功能一目了然
- 实时反馈，操作结果立即可见

### 3. 数据持久化
- 所有设置自动保存到服务器
- 支持项目间的数据独立管理
- 完整的属性恢复机制

### 4. 扩展性强
- 支持多种图片格式和尺寸
- 可扩展支持更多地理功能
- 模块化设计，易于维护和升级

## 总结
通过这次优化，图片与卫星地图的重合功能达到了专业GIS软件的水准，实现了真正的地理坐标一致性和便捷的操作体验。用户现在可以精确地将机场图片覆盖到对应的地理位置，并且在任何地图缩放级别下都能保持准确的位置关系。
