#!/usr/bin/env python3
"""
路径规划模块
实现A*算法进行导航网格上的路径搜索
"""

import heapq
import math
from typing import List, Dict, Tuple, Optional
import uuid


class PathFinder:
    def __init__(self):
        self.paths = {}  # 存储计算的路径
    
    def find_path(self, grid_data: Dict, start_point: Tuple[float, float], 
                  end_point: Tuple[float, float]) -> Optional[Dict]:
        """
        使用A*算法在导航网格上寻找路径
        
        Args:
            grid_data: 网格数据，包含nodes和adjacency
            start_point: 起点坐标 (lat, lon)
            end_point: 终点坐标 (lat, lon)
            
        Returns:
            路径数据字典，包含路径点和距离信息
        """
        if not grid_data or 'nodes' not in grid_data or 'adjacency' not in grid_data:
            print("无效的网格数据")
            return None
        
        nodes = grid_data['nodes']
        adjacency = grid_data['adjacency']
        
        print(f"开始路径规划: 起点{start_point} -> 终点{end_point}")
        print(f"网格节点数: {len(nodes)}")
        
        # 找到最近的起点和终点节点
        start_node = self._find_nearest_node(start_point, nodes)
        end_node = self._find_nearest_node(end_point, nodes)
        
        if start_node is None or end_node is None:
            print(f"无法找到起点或终点的最近节点: start={start_node}, end={end_node}")
            return None
        
        print(f"起点节点: {start_node} {nodes[start_node]}")
        print(f"终点节点: {end_node} {nodes[end_node]}")
        
        # 执行A*搜索
        path_nodes = self._astar_search(start_node, end_node, nodes, adjacency)
        
        if not path_nodes:
            print("未找到路径")
            return None
        
        # 转换为坐标路径
        path_coords = [nodes[node_idx] for node_idx in path_nodes]
        
        # 计算路径总距离
        total_distance = self._calculate_path_distance(path_coords)
        
        # 路径平滑处理
        smoothed_path = self._smooth_path(path_coords)
        
        path_data = {
            'id': str(uuid.uuid4()),
            'start_point': start_point,
            'end_point': end_point,
            'start_node': start_node,
            'end_node': end_node,
            'node_path': path_nodes,
            'coordinate_path': path_coords,
            'smoothed_path': smoothed_path,
            'total_distance': total_distance,
            'node_count': len(path_nodes),
            'created_at': None
        }
        
        # 存储路径
        self.paths[path_data['id']] = path_data
        
        print(f"路径规划完成: {len(path_nodes)}个节点, 总距离{total_distance:.2f}米")
        
        return path_data
    
    def _find_nearest_node(self, point: Tuple[float, float], nodes: List[List[float]]) -> Optional[int]:
        """找到最近的网格节点"""
        if not nodes:
            return None
        
        min_distance = float('inf')
        nearest_node = None
        
        for i, node in enumerate(nodes):
            # node格式: [lat, lon]
            distance = self._calculate_distance(point, (node[0], node[1]))
            if distance < min_distance:
                min_distance = distance
                nearest_node = i
        
        return nearest_node
    
    def _calculate_distance(self, point1: Tuple[float, float], point2: Tuple[float, float]) -> float:
        """计算两点间的距离（米）"""
        lat1, lon1 = point1
        lat2, lon2 = point2
        
        # 使用Haversine公式计算地球表面两点间距离
        R = 6371000  # 地球半径（米）
        
        lat1_rad = math.radians(lat1)
        lat2_rad = math.radians(lat2)
        delta_lat = math.radians(lat2 - lat1)
        delta_lon = math.radians(lon2 - lon1)
        
        a = (math.sin(delta_lat / 2) ** 2 + 
             math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(delta_lon / 2) ** 2)
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
        
        return R * c
    
    def _astar_search(self, start: int, goal: int, nodes: List[List[float]], 
                     adjacency: Dict[str, List[int]]) -> Optional[List[int]]:
        """A*搜索算法"""
        # 将adjacency的键转换为整数
        adj_dict = {}
        for key, neighbors in adjacency.items():
            adj_dict[int(key)] = neighbors
        
        # 开放列表和关闭列表
        open_list = []
        closed_set = set()
        
        # g值：从起点到当前节点的实际距离
        g_score = {start: 0}
        
        # f值：g值 + h值（启发式距离）
        f_score = {start: self._heuristic(start, goal, nodes)}
        
        # 父节点记录，用于重建路径
        came_from = {}
        
        # 将起点加入开放列表
        heapq.heappush(open_list, (f_score[start], start))
        
        while open_list:
            # 取出f值最小的节点
            current_f, current = heapq.heappop(open_list)
            
            if current in closed_set:
                continue
            
            # 到达目标
            if current == goal:
                return self._reconstruct_path(came_from, current)
            
            closed_set.add(current)
            
            # 检查所有邻居节点
            neighbors = adj_dict.get(current, [])
            for neighbor in neighbors:
                if neighbor in closed_set:
                    continue
                
                # 计算到邻居的距离
                tentative_g = g_score[current] + self._calculate_distance(
                    (nodes[current][0], nodes[current][1]),
                    (nodes[neighbor][0], nodes[neighbor][1])
                )
                
                if neighbor not in g_score or tentative_g < g_score[neighbor]:
                    # 找到更好的路径
                    came_from[neighbor] = current
                    g_score[neighbor] = tentative_g
                    f_score[neighbor] = tentative_g + self._heuristic(neighbor, goal, nodes)
                    
                    heapq.heappush(open_list, (f_score[neighbor], neighbor))
        
        return None  # 未找到路径
    
    def _heuristic(self, node1: int, node2: int, nodes: List[List[float]]) -> float:
        """启发式函数：估算两节点间的距离"""
        return self._calculate_distance(
            (nodes[node1][0], nodes[node1][1]),
            (nodes[node2][0], nodes[node2][1])
        )
    
    def _reconstruct_path(self, came_from: Dict[int, int], current: int) -> List[int]:
        """重建路径"""
        path = [current]
        while current in came_from:
            current = came_from[current]
            path.append(current)
        path.reverse()
        return path
    
    def _calculate_path_distance(self, path_coords: List[List[float]]) -> float:
        """计算路径总距离"""
        if len(path_coords) < 2:
            return 0.0
        
        total_distance = 0.0
        for i in range(1, len(path_coords)):
            distance = self._calculate_distance(
                (path_coords[i-1][0], path_coords[i-1][1]),
                (path_coords[i][0], path_coords[i][1])
            )
            total_distance += distance
        
        return total_distance
    
    def _smooth_path(self, path_coords: List[List[float]]) -> List[List[float]]:
        """路径平滑处理"""
        if len(path_coords) < 3:
            return path_coords
        
        smoothed = [path_coords[0]]  # 保留起点
        
        # 简单的路径平滑：移除共线的中间点
        for i in range(1, len(path_coords) - 1):
            prev_point = path_coords[i-1]
            curr_point = path_coords[i]
            next_point = path_coords[i+1]
            
            # 计算角度变化
            angle_change = self._calculate_angle_change(prev_point, curr_point, next_point)
            
            # 如果角度变化大于阈值，保留这个点
            if abs(angle_change) > 0.1:  # 约5.7度
                smoothed.append(curr_point)
        
        smoothed.append(path_coords[-1])  # 保留终点
        
        return smoothed
    
    def _calculate_angle_change(self, p1: List[float], p2: List[float], p3: List[float]) -> float:
        """计算三点间的角度变化"""
        # 计算两个向量
        v1 = [p2[0] - p1[0], p2[1] - p1[1]]
        v2 = [p3[0] - p2[0], p3[1] - p2[1]]
        
        # 计算向量的模长
        len1 = math.sqrt(v1[0]**2 + v1[1]**2)
        len2 = math.sqrt(v2[0]**2 + v2[1]**2)
        
        if len1 == 0 or len2 == 0:
            return 0
        
        # 计算夹角
        dot_product = v1[0] * v2[0] + v1[1] * v2[1]
        cos_angle = dot_product / (len1 * len2)
        
        # 防止数值误差
        cos_angle = max(-1, min(1, cos_angle))
        
        return math.acos(cos_angle)
    
    def get_path(self, path_id: str) -> Optional[Dict]:
        """获取指定路径"""
        return self.paths.get(path_id)
    
    def get_all_paths(self) -> Dict[str, Dict]:
        """获取所有路径"""
        return self.paths.copy()
    
    def clear_paths(self):
        """清除所有路径"""
        self.paths.clear()
